package file.engine.services.utils;

import file.engine.configs.AllConfigs;
import file.engine.configs.Constants;
import file.engine.entity.Pair;
import file.engine.services.DatabaseService;
import file.engine.utils.PinyinUtil;
import file.engine.utils.RegexUtil;
import file.engine.utils.file.FileUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.TreeSet;
import java.util.regex.Pattern;

@Slf4j
@SuppressWarnings({"IndexOfReplaceableByContains"})
public class PathMatchUtil {

    /**
     * 判断文件路径是否满足当前匹配结果（该方法由check方法使用），检查文件路径使用check方法。
     *
     * @param fileName     文件名
     * @param parentPath   文件路径
     * @param isIgnoreCase 是否忽略大小写
     * @return 如果匹配成功则返回true
     * @see #check (String, String, String[], boolean, String, String[], String[], boolean[], boolean)
     */
    private static Constants.Enums.PathMatchType notMatched(String fileName,
                                                            String parentPath,
                                                            boolean isIgnoreCase,
                                                            String[] keywords,
                                                            String[] keywordsLowerCase,
                                                            boolean[] isKeywordPath,
                                                            boolean isEnableFuzzyMatch) {
        boolean fullMatched = false;
        boolean hasMatchedOptionalKeyword = false;  // 添加标志判断是否有可选关键词匹配成功
        int optionalKeywordCount = 0;  // 统计可选关键字的数量

        // 先统计可选关键字数量
        final int length = keywords.length;
        for (String eachKeyword : keywords) {
            if (eachKeyword != null && eachKeyword.startsWith("?")) {
                optionalKeywordCount++;
            }
        }

        for (int i = 0; i < length; ++i) {
            String eachKeyword;
            final boolean isPath = isKeywordPath[i];
            String matcherStrFromFilePath = isPath ? parentPath : fileName;
            if (isIgnoreCase) {
                eachKeyword = keywordsLowerCase[i];
                matcherStrFromFilePath = matcherStrFromFilePath.toLowerCase();
            } else {
                eachKeyword = keywords[i];
            }
            if (eachKeyword == null || eachKeyword.isEmpty()) {
                continue;
            }

            // 检查是否是可选关键词（以?开头）
            boolean isOptionalKeyword = eachKeyword.startsWith("?");
            if (isOptionalKeyword) {
                // 移除开头的?字符
                eachKeyword = eachKeyword.substring(1);
                // 先尝试精确匹配
                if (matcherStrFromFilePath.indexOf(eachKeyword) != -1) {
                    hasMatchedOptionalKeyword = true;
                    fullMatched = true;
                    continue;
                }
                // 尝试模糊匹配和拼音匹配
                if (isEnableFuzzyMatch && patternCharMatch(eachKeyword, matcherStrFromFilePath)) {
                    hasMatchedOptionalKeyword = true;
                    continue;
                }
                // 如果关键字长度大于4且包含中文，尝试拼音匹配
                if (PinyinUtil.isStringContainChinese(matcherStrFromFilePath)) {
                    String pinyin = PinyinUtil.toPinyin(matcherStrFromFilePath, ",");
                    String[] pinyinList = RegexUtil.comma.split(pinyin);

                    StringBuilder fullPinyin = new StringBuilder();
                    StringBuilder pinyinInitials = new StringBuilder();
                    for (String eachPinyin : pinyinList) {
                        if (eachPinyin.isEmpty()) {
                            continue;
                        }
                        pinyinInitials.append(eachPinyin.charAt(0));
                        fullPinyin.append(eachPinyin);
                    }
                    // 尝试拼音直接匹配或拼音首字母匹配
                    if (patternCharMatch(eachKeyword, fullPinyin.toString()) || pinyinInitials.indexOf(eachKeyword) != -1) {
                        hasMatchedOptionalKeyword = true;
                    }
                }
                // 如果是可选关键词但没匹配上，继续检查下一个关键词
                continue;
            }

            //开始匹配
            boolean keywordNotMatched = matcherStrFromFilePath.indexOf(eachKeyword) == -1;
            if (keywordNotMatched) {
                if (isPath) {
                    return Constants.Enums.PathMatchType.NOT_MATCHED;
                }
                if (!isEnableFuzzyMatch || !patternCharMatch(eachKeyword, matcherStrFromFilePath)) {
                    //路径匹配或不含中文，直接返回匹配失败
                    if (!PinyinUtil.isStringContainChinese(matcherStrFromFilePath)) {
                        return Constants.Enums.PathMatchType.NOT_MATCHED;
                    } else {
                        //模糊匹配未成功，尝试拼音匹配
                        String pinyin = PinyinUtil.toPinyin(matcherStrFromFilePath, ",");
                        String[] pinyinList = RegexUtil.comma.split(pinyin);

                        StringBuilder fullPinyin = new StringBuilder();
                        StringBuilder pinyinInitials = new StringBuilder();
                        for (String eachPinyin : pinyinList) {
                            if (eachPinyin.isEmpty()) {
                                continue;
                            }
                            pinyinInitials.append(eachPinyin.charAt(0));
                            fullPinyin.append(eachPinyin);
                        }
                        // 使用patternCharMatch匹配拼音（支持完整拼音和首字母混合）
                        if (patternCharMatch(eachKeyword, fullPinyin.toString())) {
                            fullMatched = true;
                        } else {
                            // 尝试拼音首字母匹配
                            if (pinyinInitials.indexOf(eachKeyword) == -1) {
                                //拼音匹配失败，所有尝试均失败，返回匹配失败
                                return Constants.Enums.PathMatchType.NOT_MATCHED;
                            }
                        }
                    }
                }
            } else {
                fullMatched = true;
            }
        }

        // 修改返回逻辑，如果有可选关键字，则必须至少有一个匹配成功
        if (optionalKeywordCount > 0) {
            // 如果一个都没匹配
            if (!hasMatchedOptionalKeyword) {
                return Constants.Enums.PathMatchType.NOT_MATCHED;
            }
        }

        //匹配成功
        if (fullMatched) {
            return Constants.Enums.PathMatchType.FULL_MATCHED;
        } else {
            return Constants.Enums.PathMatchType.FUZZY_MATCHED;
        }
    }

    private static boolean patternCharMatch(String pattern, String str) {
        int patternIndex = 0;
        int lastMatchedIndex = -1;  // 上一个匹配的字符在str中的位置
        int strIndex = 0;
        int continuousMatchLength = 0;  // 当前连续匹配的长度
        int maxContinuousMatch = 0;     // 最大连续匹配长度

        int patternLength = pattern.length();
        int strLength = str.length();
        boolean enableDistanceCheck = strLength > 35;  // 只有超长文件名才启用距离检查

        while (patternIndex < patternLength && strIndex < strLength) {
            char currentChar = str.charAt(strIndex);

            // 遇到空格时重置lastMatchedIndex为空格的位置
            if (currentChar == ' ') {
                lastMatchedIndex = strIndex;
            }

            // 如果字符匹配，则移动pattern的指针
            if (pattern.charAt(patternIndex) == currentChar) {
                // 只有在超长文件名时才检查距离，如果不是第一个字符，检查当前匹配字符距离上一个匹配字符或者最近的空格是否超过3个字符
                if (enableDistanceCheck && lastMatchedIndex != -1 && strIndex - lastMatchedIndex > 4) {  // 距离超过3个字符
                    return false;
                }

                // 检查是否连续匹配
                if (lastMatchedIndex != -1 && strIndex == lastMatchedIndex + 1) {
                    continuousMatchLength++;
                } else {
                    continuousMatchLength = 1;  // 重新开始计算连续匹配
                }

                // 更新最大连续匹配长度
                maxContinuousMatch = Math.max(maxContinuousMatch, continuousMatchLength);

                lastMatchedIndex = strIndex;
                patternIndex++;
            }
            // 无论是否匹配，移动目标字符串的指针
            strIndex++;
        }
        // 如果匹配完了整个pattern，并且至少有2个字符连续匹配，说明匹配成功
        return patternIndex == patternLength && maxContinuousMatch >= 2;
    }

    private static boolean fuzzyMatch(String pattern, String str) {
        if (str.length() < pattern.length()) {
            return false;
        }
        int maxDistance;
        if (pattern.length() > 9) {
            maxDistance = 2;
        } else {
            maxDistance = 1;
        }
        int startPos = 0;
        while (startPos <= str.length() - pattern.length()) {
            String subStr = str.substring(startPos, Math.min(startPos + pattern.length(), str.length()));
            int distance = calcStrDistance(pattern, subStr);
            if (distance <= maxDistance) {
                return true;
            }
            startPos++;
        }
        return false;
    }

    /**
     * 计算字符串相差的错误字符
     *
     * @param pattern 匹配规则
     * @param str     待匹配字符串
     * @return str与pattern相差几个错误字符
     */
    private static int calcStrDistance(String pattern, String str) {
        int m = pattern.length();
        int n = str.length();

        if (m > n) {
            return Integer.MAX_VALUE;
        }

        int[][] dp = new int[m + 1][n + 1];

        for (int i = 0; i <= m; i++) {
            for (int j = 0; j <= n; j++) {
                if (i == 0) {
                    dp[i][j] = j; // 将pattern转换为空字符串的步骤数
                } else if (j == 0) {
                    dp[i][j] = i; // 将str转换为空字符串的步骤数
                } else if (pattern.charAt(i - 1) == str.charAt(j - 1)) {
                    dp[i][j] = dp[i - 1][j - 1]; // 如果字符相同，不需要额外步骤
                } else {
                    dp[i][j] = 1 + Math.min(Math.min(dp[i - 1][j],  // 删除
                                    dp[i][j - 1]), // 插入
                            dp[i - 1][j - 1]);       // 替换
                }
            }
        }
        return dp[m][n];
    }

    /**
     * 检查文件路径是否匹配所有输入规则
     *
     * @param fileName           文件名
     * @param parentPath         将要匹配的文件路径
     * @param searchCase         匹配规则 f d case full
     * @param isIgnoreCase       当searchCase中包含case则为false，该变量用于防止searchCase重复计算
     * @param searchText         用户输入字符串，由关键字通过 ; 连接
     * @param keywords           用户输入字符串生成的关键字
     * @param keywordsLowerCase  防止重复计算
     * @param isKeywordPath      keyword是否为路径或者文件名
     * @param isEnableFuzzyMatch 是否启用模糊匹配
     * @return true如果满足所有条件 否则返回false
     */
    public static Constants.Enums.PathMatchType check(String fileName,
                                                      String parentPath,
                                                      String[] searchCase,
                                                      boolean isIgnoreCase,
                                                      String searchText,
                                                      String[] keywords,
                                                      String[] keywordsLowerCase,
                                                      boolean[] isKeywordPath,
                                                      boolean isEnableFuzzyMatch) {
        Constants.Enums.PathMatchType matchType;
        if (searchCase != null && List.of(searchCase).contains(SearchCase.CONTENT)) {
            return Constants.Enums.PathMatchType.NOT_MATCHED;
        }
        String path = parentPath + File.separator + fileName;
        if (searchCase != null && List.of(searchCase).contains(SearchCase.PATTERN)) {
            Pattern pattern = RegexUtil.getPattern(searchText, 0);
            if (!pattern.matcher(path).matches()) {
                return Constants.Enums.PathMatchType.NOT_MATCHED;
            } else {
                matchType = Constants.Enums.PathMatchType.FULL_MATCHED;
            }
        } else {
            matchType = notMatched(fileName, parentPath, isIgnoreCase, keywords, keywordsLowerCase, isKeywordPath, isEnableFuzzyMatch);
            if (Constants.Enums.PathMatchType.NOT_MATCHED == matchType) {
                return Constants.Enums.PathMatchType.NOT_MATCHED;
            }
        }
        if (searchCase == null) {
            return matchType;
        }
        for (String eachCase : searchCase) {
            switch (eachCase) {
                case SearchCase.FILE -> {
                    if (!Files.isRegularFile(Path.of(path))) {
                        return Constants.Enums.PathMatchType.NOT_MATCHED;
                    }
                }
                case SearchCase.DIRECTORY -> {
                    if (!Files.isDirectory(Path.of(path))) {
                        return Constants.Enums.PathMatchType.NOT_MATCHED;
                    }
                }
                case SearchCase.FULL -> {
                    if (!searchText.equalsIgnoreCase(FileUtil.getFileName(path))) {
                        return Constants.Enums.PathMatchType.NOT_MATCHED;
                    }
                }
            }
        }
        //所有规则均已匹配
        return matchType;
    }

    public static Pair<String, String> highlightKeywords(String fileName,
                                                         String parentPath,
                                                         DatabaseService.SearchInfo searchInfo) {
        boolean enableFuzzyMatch = AllConfigs
                .getInstance()
                .getConfigEntity()
                .isEnableFuzzyMatch();

        return highlightKeywords(
                fileName,
                parentPath,
                searchInfo.isIgnoreCase(),
                searchInfo.keywords(),
                searchInfo.keywordsLowerCase(),
                searchInfo.isKeywordPath(),
                enableFuzzyMatch
        );
    }

    /**
     * 为匹配的关键字添加高亮标记
     *
     * @param fileName           文件名
     * @param parentPath         文件路径
     * @param isIgnoreCase       是否忽略大小写
     * @param keywords           关键字数组
     * @param keywordsLowerCase  关键字小写版本
     * @param isKeywordPath      每个关键字是否为路径关键字
     * @param isEnableFuzzyMatch 是否启用模糊匹配
     * @return 高亮后的完整路径字符串（parentPath, fileName）
     */
    public static Pair<String, String> highlightKeywords(String fileName,
                                                         String parentPath,
                                                         boolean isIgnoreCase,
                                                         String[] keywords,
                                                         String[] keywordsLowerCase,
                                                         boolean[] isKeywordPath,
                                                         boolean isEnableFuzzyMatch) {
        try {
            // 收集文件名和路径的高亮位置
            Set<Integer> fileNameHighlights = new TreeSet<>();
            Set<Integer> parentPathHighlights = new TreeSet<>();

            final int length = keywords.length;
            for (int i = 0; i < length; ++i) {
                String eachKeyword;
                final boolean isPath = isKeywordPath[i];
                String matcherStrFromFilePath = isPath ? parentPath : fileName;

                if (isIgnoreCase) {
                    eachKeyword = keywordsLowerCase[i];
                    matcherStrFromFilePath = matcherStrFromFilePath.toLowerCase();
                } else {
                    eachKeyword = keywords[i];
                }

                if (eachKeyword == null || eachKeyword.isEmpty()) {
                    continue;
                }

                // 处理可选关键字
                boolean isOptionalKeyword = eachKeyword.startsWith("?");
                if (isOptionalKeyword) {
                    eachKeyword = eachKeyword.substring(1);
                    if (eachKeyword.isEmpty()) {
                        continue;
                    }

                    // 对于可选关键字，需要先检查是否匹配成功，只有匹配成功才进行高亮
                    boolean optionalKeywordMatched = false;

                    // 先尝试精确匹配
                    if (matcherStrFromFilePath.indexOf(eachKeyword) != -1) {
                        optionalKeywordMatched = true;
                    }
                    // 尝试模糊匹配
                    else if (isEnableFuzzyMatch && patternCharMatch(eachKeyword, matcherStrFromFilePath)) {
                        optionalKeywordMatched = true;
                    }
                    // 如果包含中文，尝试拼音匹配
                    else if (PinyinUtil.isStringContainChinese(matcherStrFromFilePath)) {
                        String pinyin = PinyinUtil.toPinyin(matcherStrFromFilePath, ",");
                        String[] pinyinList = RegexUtil.comma.split(pinyin);

                        StringBuilder fullPinyin = new StringBuilder();
                        StringBuilder pinyinInitials = new StringBuilder();
                        for (String eachPinyin : pinyinList) {
                            if (eachPinyin.isEmpty()) {
                                continue;
                            }
                            pinyinInitials.append(eachPinyin.charAt(0));
                            fullPinyin.append(eachPinyin);
                        }
                        // 尝试拼音直接匹配或拼音首字母匹配
                        if (patternCharMatch(eachKeyword, fullPinyin.toString()) || pinyinInitials.indexOf(eachKeyword) != -1) {
                            optionalKeywordMatched = true;
                        }
                    }

                    // 如果可选关键字没有匹配成功，跳过高亮
                    if (!optionalKeywordMatched) {
                        continue;
                    }
                }

                // 根据关键字类型收集高亮位置
                if (isPath) {
                    collectHighlightPositions(parentPath,
                            eachKeyword,
                            isIgnoreCase,
                            isEnableFuzzyMatch,
                            parentPathHighlights);
                } else {
                    collectHighlightPositions(fileName,
                            eachKeyword,
                            isIgnoreCase,
                            isEnableFuzzyMatch,
                            fileNameHighlights);
                }
            }

            // 应用高亮标签
            String highlightedFileName = applyHighlights(fileName, fileNameHighlights);
            String highlightedParentPath = applyHighlights(parentPath, parentPathHighlights);

            return new Pair<>(highlightedParentPath, highlightedFileName);
        } catch (Exception e) {
            log.error("Error: {}", e.getMessage(), e);
            return new Pair<>(parentPath, fileName);
        }
    }

    /**
     * 收集关键字在字符串中的高亮位置
     *
     * @param targetString       目标字符串
     * @param keyword            关键字
     * @param isIgnoreCase       是否忽略大小写
     * @param isEnableFuzzyMatch 是否启用模糊匹配
     * @param highlightPositions 高亮位置集合
     */
    private static void collectHighlightPositions(String targetString,
                                                  String keyword,
                                                  boolean isIgnoreCase,
                                                  boolean isEnableFuzzyMatch,
                                                  Set<Integer> highlightPositions) {
        if (keyword == null || keyword.isEmpty()) {
            return;
        }

        String searchString = isIgnoreCase ? targetString.toLowerCase() : targetString;
        String searchKeyword = isIgnoreCase ? keyword.toLowerCase() : keyword;

        // 1. 尝试精确匹配
        collectExactMatchPositions(searchString, searchKeyword, highlightPositions);

        // 2. 尝试模糊匹配
        if (isEnableFuzzyMatch) {
            collectFuzzyMatchPositions(searchKeyword, searchString, highlightPositions);
        }

        // 3. 尝试拼音匹配（仅对中文）
        if (PinyinUtil.isStringContainChinese(targetString)) {
            collectPinyinMatchPositions(targetString, searchKeyword, highlightPositions);
        }
    }

    /**
     * 收集精确匹配的位置
     */
    private static void collectExactMatchPositions(String searchString,
                                                   String searchKeyword,
                                                   Set<Integer> highlightPositions) {
        int index = searchString.indexOf(searchKeyword);
        while (index != -1) {
            for (int i = index; i < index + searchKeyword.length(); i++) {
                highlightPositions.add(i);
            }
            index = searchString.indexOf(searchKeyword, index + 1);
        }
    }

    /**
     * 收集模糊匹配的位置
     */
    private static void collectFuzzyMatchPositions(String pattern,
                                                   String str,
                                                   Set<Integer> highlightPositions) {
        int[] matchedPositions = findFuzzyMatchPositions(pattern, str);
        for (int pos : matchedPositions) {
            highlightPositions.add(pos);
        }
    }

    /**
     * 收集拼音匹配的位置
     */
    private static void collectPinyinMatchPositions(String originalString,
                                                    String searchKeyword,
                                                    Set<Integer> highlightPositions) {
        String pinyin = PinyinUtil.toPinyin(originalString, ",");
        String[] pinyinList = RegexUtil.comma.split(pinyin);

        StringBuilder fullPinyin = new StringBuilder();
        StringBuilder pinyinInitials = new StringBuilder();

        for (String eachPinyin : pinyinList) {
            if (eachPinyin.isEmpty()) {
                continue;
            }
            pinyinInitials.append(eachPinyin.charAt(0));
            fullPinyin.append(eachPinyin);
        }

        // 检查完整拼音匹配
        if (patternCharMatch(searchKeyword, fullPinyin.toString())) {
            collectPinyinCharacterPositions(pinyinList, searchKeyword, highlightPositions, false);
        }
        // 检查拼音首字母匹配
        else if (pinyinInitials.indexOf(searchKeyword) != -1) {
            collectPinyinCharacterPositions(pinyinList, searchKeyword, highlightPositions, true);
        }
    }

    /**
     * 根据拼音匹配收集字符位置
     */
    private static void collectPinyinCharacterPositions(String[] pinyinList,
                                                        String searchKeyword,
                                                        Set<Integer> highlightPositions,
                                                        boolean isInitials) {
        if (isInitials) {
            // 首字母匹配：使用indexOf找到匹配位置
            StringBuilder pinyinInitials = new StringBuilder();
            for (String eachPinyin : pinyinList) {
                if (!eachPinyin.isEmpty()) {
                    pinyinInitials.append(eachPinyin.charAt(0));
                }
            }

            int matchIndex = pinyinInitials.toString().indexOf(searchKeyword);
            if (matchIndex != -1) {
                int endCharIndex = Math.min(matchIndex + searchKeyword.length(), pinyinList.length);
                for (int i = matchIndex; i < endCharIndex; i++) {
                    highlightPositions.add(i);
                }
            }
        } else {
            // 完整拼音匹配：检查每个中文字符是否有至少2个连续字母匹配
            for (int i = 0; i < pinyinList.length; i++) {
                if (pinyinList[i].isEmpty()) {
                    continue;
                }
                // 检查当前字符的拼音是否有至少2个连续字母匹配
                if (hasMinimumContinuousMatch(searchKeyword, pinyinList[i], 2)) {
                    highlightPositions.add(i);
                }
            }
        }
    }

    /**
     * 将高亮位置应用到字符串上
     *
     * @param targetString       目标字符串
     * @param highlightPositions 高亮位置集合
     * @return 应用高亮后的字符串
     */
    private static String applyHighlights(String targetString, Set<Integer> highlightPositions) {
        if (highlightPositions.isEmpty()) {
            return targetString;
        }

        // 将连续的位置合并成区间
        List<int[]> intervals = new ArrayList<>();
        int start = -1;
        int end = -1;

        for (int pos : highlightPositions) {
            if (pos >= targetString.length()) {
                continue;
            }

            if (start == -1) {
                start = end = pos;
            } else if (pos == end + 1) {
                end = pos;
            } else {
                intervals.add(new int[]{start, end});
                start = end = pos;
            }
        }

        if (start != -1) {
            intervals.add(new int[]{start, end});
        }

        // 从后往前应用高亮，避免索引偏移
        StringBuilder result = new StringBuilder(targetString);
        for (int i = intervals.size() - 1; i >= 0; i--) {
            int[] interval = intervals.get(i);
            int startPos = interval[0];
            int endPos = interval[1] + 1;

            result.insert(endPos, "</highlight>");
            result.insert(startPos, "<highlight>");
        }

        return result.toString();
    }

    /**
     * 找到模糊匹配的字符位置
     */
    private static int[] findFuzzyMatchPositions(String pattern, String str) {
        int patternIndex = 0;
        int lastMatchedIndex = -1;  // 上一个匹配的字符在str中的位置
        int strIndex = 0;
        int continuousMatchLength = 0;  // 当前连续匹配的长度
        int maxContinuousMatch = 0;     // 最大连续匹配长度

        int patternLength = pattern.length();
        int strLength = str.length();
        boolean enableDistanceCheck = strLength > 35;  // 只有超长文件名才启用距离检查

        int[] tempPositions = new int[pattern.length()];
        int posCount = 0;

        while (patternIndex < patternLength && strIndex < strLength) {
            char currentChar = str.charAt(strIndex);

            // 遇到空格时重置lastMatchedIndex为空格的位置
            if (currentChar == ' ') {
                lastMatchedIndex = strIndex;
            }

            // 如果字符匹配，则移动pattern的指针
            if (pattern.charAt(patternIndex) == currentChar) {
                // 只有在超长文件名时才检查距离，如果不是第一个字符，检查当前匹配字符距离上一个匹配字符或者最近的空格是否超过3个字符
                if (enableDistanceCheck && lastMatchedIndex != -1 && strIndex - lastMatchedIndex > 4) {  // 距离超过3个字符
                    return new int[0];
                }

                // 检查是否连续匹配
                if (lastMatchedIndex != -1 && strIndex == lastMatchedIndex + 1) {
                    continuousMatchLength++;
                } else {
                    continuousMatchLength = 1;  // 重新开始计算连续匹配
                }

                // 更新最大连续匹配长度
                maxContinuousMatch = Math.max(maxContinuousMatch, continuousMatchLength);

                tempPositions[posCount++] = strIndex;
                lastMatchedIndex = strIndex;
                patternIndex++;
            }
            // 无论是否匹配，移动目标字符串的指针
            strIndex++;
        }

        // 如果匹配完了整个pattern，并且至少有2个字符连续匹配，说明匹配成功
        if (patternIndex == patternLength && maxContinuousMatch >= 2) {
            int[] result = new int[posCount];
            System.arraycopy(tempPositions, 0, result, 0, posCount);
            return result;
        }

        return new int[0];
    }

    /**
     * 检查模式字符串是否能从目标字符串的首字母开始进行至少指定长度的连续匹配
     *
     * @param pattern   模式字符串
     * @param target    目标字符串（拼音）
     * @param minLength 最小连续匹配长度
     * @return 是否满足条件
     */
    private static boolean hasMinimumContinuousMatch(String pattern,
                                                     String target,
                                                     @SuppressWarnings("SameParameterValue") int minLength) {
        if (pattern.length() < minLength || target.length() < minLength) {
            return false;
        }

        // 遍历模式字符串的每个位置，寻找能从目标字符串首字母开始匹配的子串
        for (int patternStart = 0; patternStart <= pattern.length() - minLength; patternStart++) {
            // 计算从目标字符串首字母开始的连续匹配长度
            int continuousMatchLength = 0;
            int targetIndex = 0;  // 始终从目标字符串的首字母开始
            int patternIndex = patternStart;

            while (targetIndex < target.length() &&
                   patternIndex < pattern.length() &&
                   target.charAt(targetIndex) == pattern.charAt(patternIndex)) {
                continuousMatchLength++;
                targetIndex++;
                patternIndex++;
            }

            // 如果连续匹配长度达到最小要求，返回true
            if (continuousMatchLength >= minLength) {
                return true;
            }
        }

        return false;
    }

    public static class SearchCase {
        public static final String DIRECTORY = "d";
        public static final String FILE = "f";
        public static final String FULL = "full";
        public static final String CASE = "case";
        public static final String PATTERN = "p";
        public static final String CONTENT = "c";
    }
}
