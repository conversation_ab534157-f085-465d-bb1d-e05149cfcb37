﻿// dllmain.cpp : Defines the entry point for the DLL application.
#include "BS_thread_pool.hpp"
#include "framework.h"
#include "file_engine_dllInterface_PathMatcher.h"
#include <string>
#include <vector>
#include "sqlite3.h"
#include <shared_mutex>
#include "path_util.h"
#include <future>
#include <atomic>
#include <ranges>

#include "hash_table6.hpp"
#include "uwp.hpp"
#include "key.h"
#pragma comment(lib, "sqlite3")

// SQLite连接池配置
// 每个数据库路径会创建SQLITE_CONNECTION_NUMBER个独立的连接
// 使用方法:
// 1. 调用openConnection(db_path)创建连接池
// 2. 调用match(sql, db_path, search_info_id, max_results, connection_id, consumer)
//    其中connection_id范围: 0 ~ (SQLITE_CONNECTION_NUMBER - 1)
// 3. 不同的connection_id可以并发执行查询
constexpr int SQLITE_CONNECTION_NUMBER = 4;

using Connection_t = emhash6::HashMap<std::string, std::vector<sqlite3*>>;


emhash6::HashMap<long, search_info*> search_info_map;
std::atomic_long search_info_id_generator = 1;
std::shared_mutex search_info_rw_lock;

emhash6::HashMap<long, Connection_t> connection_map;
std::atomic_long connection_id_generator = 1;
std::shared_mutex connection_rw_lock;

BS::thread_pool search_thread_pool;

constexpr auto one_second = std::chrono::seconds(1);
const auto processor_count = std::thread::hardware_concurrency();

jclass g_search_result_class = nullptr;
jclass g_search_result_consumer_class = nullptr;
jclass g_uwp_consumer_class = nullptr;
jclass g_uwp_result_class = nullptr;
jmethodID g_uwp_result_constructor = nullptr;
jmethodID g_search_result_constructor = nullptr;
jmethodID g_search_result_accept_func = nullptr;
jmethodID g_uwp_consumer_accept_func = nullptr;
std::once_flag java_class_flag;
std::once_flag java_uwp_class_flag;

BS::multi_future<void> submit_search_task(const unsigned int path_vector_count,
	const std::vector<path_match_struct>& path_vector,
	search_info* search_info_ptr,
	Concurrency::concurrent_vector<search_result*>& matched_path_vec);
void init_search_result_class(JNIEnv* env, jobject result_consumer);
void init_uwp_result_class(JNIEnv* env, jobject uwp_result_consumer);
jstring wstring2jstring(JNIEnv* env, const std::wstring& wstr);

void generate_search_case(JNIEnv* env, std::vector<std::string>& search_case_vec, const jobjectArray search_case)
{
	const auto search_case_len = env->GetArrayLength(search_case);
	for (jsize i = 0; i < search_case_len; ++i)
	{
		if (const auto search_case_str = env->GetObjectArrayElement(search_case, i); search_case_str != nullptr)
		{
			const auto tmp_search_case_str = reinterpret_cast<jstring>(search_case_str);
			auto search_case_chars = env->GetStringUTFChars(tmp_search_case_str, nullptr);
			search_case_vec.emplace_back(search_case_chars);
			env->ReleaseStringUTFChars(tmp_search_case_str, search_case_chars);
			env->DeleteLocalRef(tmp_search_case_str);
		}
	}
}

void is_path_matched_keywords(const path_match_struct& path_info,
	search_info* search_info,
	Concurrency::concurrent_vector<search_result*>& matched_path_vec)
{
	const auto& parent_path_var = path_info.parent_path_var;
	const auto& file_name_var = path_info.file_name_var;
	if (const int match_type = match_func(parent_path_var, file_name_var, search_info);
		match_type == 0 || match_type == 1)
	{
		search_result* result = new search_result();
		result->path = std::string(parent_path_var) + file_name_var;
		result->file_size = path_info.file_size;
		result->modify_date = decode_file_time(path_info.modify_time);
		result->fuzzy_matched = match_type == 1;

		++search_info->result_counter;
		matched_path_vec.push_back(result);
	}
}

bool get_search_info_obj(JNIEnv* env, jobjectArray search_case, jboolean is_ignore_case, jstring search_text,
	jobjectArray keywords, jobjectArray keywords_lower, jbooleanArray is_keyword_path,
	jboolean enable_fuzzy_match, search_info** info_obj)
{
	int search_case_num = 0;
	{
		std::vector<std::string> search_case_vec;
		if (search_case != nullptr)
		{
			generate_search_case(env, search_case_vec, search_case);
		}
		for (auto& each_case : search_case_vec)
		{
			if (each_case == "f")
			{
				search_case_num |= 1;
			}
			else if (each_case == "d")
			{
				search_case_num |= 1 << 1;
			}
			else if (each_case == "full")
			{
				search_case_num |= 1 << 2;
			}
			else if (each_case == "p")
			{
				search_case_num |= 1 << 3;
			}
		}
	}
	std::vector<std::string> keywords_vec;
	std::vector<std::string> keywords_lower_vec;
	const auto keywords_length = env->GetArrayLength(keywords);
	if (keywords_length > MAX_KEYWORDS_NUMBER)
	{
		return true;
	}
	std::vector<bool> is_keyword_path_vec;
	const auto is_keyword_path_ptr_bool_array = env->GetBooleanArrayElements(is_keyword_path, nullptr);
	for (jsize i = 0; i < keywords_length; ++i)
	{
		auto tmp_keywords_str = reinterpret_cast<jstring>(env->GetObjectArrayElement(keywords, i));
		auto keywords_chars = env->GetStringUTFChars(tmp_keywords_str, nullptr);
		keywords_vec.emplace_back(keywords_chars);
		env->ReleaseStringUTFChars(tmp_keywords_str, keywords_chars);
		env->DeleteLocalRef(tmp_keywords_str);

		tmp_keywords_str = reinterpret_cast<jstring>(env->GetObjectArrayElement(keywords_lower, i));
		keywords_chars = env->GetStringUTFChars(tmp_keywords_str, nullptr);
		keywords_lower_vec.emplace_back(keywords_chars);
		env->ReleaseStringUTFChars(tmp_keywords_str, keywords_chars);
		env->DeleteLocalRef(tmp_keywords_str);
		is_keyword_path_vec.emplace_back(is_keyword_path_ptr_bool_array[i]);
	}
	env->ReleaseBooleanArrayElements(is_keyword_path, is_keyword_path_ptr_bool_array, JNI_ABORT);
	const auto search_text_chars = env->GetStringUTFChars(search_text, nullptr);
	*info_obj = new search_info(search_case_num,
		is_ignore_case,
		search_text_chars,
		keywords_vec,
		keywords_lower_vec,
		is_keyword_path_vec,
		enable_fuzzy_match);
	return false;
}

JNIEXPORT jlong JNICALL Java_file_engine_dllInterface_PathMatcher_prepareSearchInfo
(JNIEnv* env, jobject, jobjectArray search_case, jboolean is_ignore_case, jstring search_text, jobjectArray keywords,
	jobjectArray keywords_lower, jbooleanArray is_keyword_path, jboolean enable_fuzzy_match)
{
	search_info* info_ptr = nullptr;
	const auto generate_failed = get_search_info_obj(env, search_case, is_ignore_case, search_text, keywords,
		keywords_lower, is_keyword_path,
		enable_fuzzy_match, &info_ptr);
	if (generate_failed || info_ptr == nullptr)
	{
		fprintf(stderr, "%s\n", "Generate search info ptr failed");
		fflush(stderr);
		return 0;
	}

	std::unique_lock lck(search_info_rw_lock);
	const auto generated_id = search_info_id_generator++;
	search_info_map.insert(std::make_pair(generated_id, info_ptr));
	return generated_id;
}

JNIEXPORT void JNICALL Java_file_engine_dllInterface_PathMatcher_releaseSearchInfo
(JNIEnv*, jobject, jlong search_info_id)
{
	const auto info_id = static_cast<long>(search_info_id);
	search_info* ptr_to_delete = nullptr;
	{
		std::shared_lock sck(search_info_rw_lock);
		const auto& prepared_info = search_info_map.find(info_id);
		if (prepared_info != search_info_map.end())
		{
			ptr_to_delete = prepared_info->second;
		}
		else
		{
			fprintf(stderr, "%s, id: %lld\n", "Search info doesn't exist", search_info_id);
			fflush(stderr);
		}
	}

	if (ptr_to_delete != nullptr)
	{
		std::unique_lock lck(search_info_rw_lock);
		search_info_map.erase(info_id);
	}
}

JNIEXPORT void JNICALL Java_file_engine_dllInterface_PathMatcher_match
(JNIEnv* env, jobject, jstring sql, jstring db_path, jlong prepared_search_info_id,
	jint max_results, jlong connection_using_id, jint thread_id, jobject result_consumer)
{
	std::shared_lock sck(search_info_rw_lock);
	const auto search_info_id = static_cast<long>(prepared_search_info_id);
	const auto search_info_iter = search_info_map.find(search_info_id);
	if (search_info_iter == search_info_map.end()) [[unlikely]]
	{
		fprintf(stderr, "%s, id: %lld\n", "Search info doesn't exist", prepared_search_info_id);
		fflush(stderr);
		return;
	}

	search_info* info_pointer = search_info_iter->second;

	const auto max_result_unsigned = static_cast<unsigned>(max_results);

	if (info_pointer->result_counter.load() > max_result_unsigned)
	{
		return;
	}

	const auto db_path_jstr = env->GetStringUTFChars(db_path, nullptr);
	const std::string db_path_str(db_path_jstr);
	env->ReleaseStringUTFChars(db_path, db_path_jstr);

	sqlite3* db = nullptr;
	try
	{
		std::shared_lock sl(connection_rw_lock);
		// get db connection
		const auto& db_connection = connection_map.at(static_cast<long>(connection_using_id));

		const auto& db_array = db_connection.at(db_path_str);

		// 验证thread_id范围
		if (thread_id < 0 || thread_id >= SQLITE_CONNECTION_NUMBER) [[unlikely]]
		{
			fprintf(stderr, "Invalid thread_id: %ld, valid range: 0-%d\n", thread_id, SQLITE_CONNECTION_NUMBER - 1);
			fflush(stderr);
			return;
		}

		db = db_array[thread_id];
	}
	catch (std::out_of_range& e)
	{
		fprintf(stderr, "Error: %s\n", e.what());
		fflush(stderr);
	}
	if (db == nullptr) [[unlikely]]
	{
		fprintf(stderr, "Database connection is null for thread_id: %ld, connection_id: %lld, db path key: %s\n",
			thread_id, connection_using_id, db_path_str.data());
		fflush(stderr);
		return;
	}

	// prepare sql stmt
	const auto sql_str = env->GetStringUTFChars(sql, nullptr);
	const std::string sql_string(sql_str);
	env->ReleaseStringUTFChars(sql, sql_str);

	sqlite3_stmt* query_stmt = nullptr;
	if (const int rc = sqlite3_prepare_v2(db, sql_string.data(), -1, &query_stmt, nullptr);
		rc != SQLITE_OK)
	{
		fprintf(stderr, "prepare sql failed, sql: %s\n", sql_string.data());
		fflush(stderr);
		return;
	}

	//execute query sql
	bool query_done = false;

	// 动态确定缓冲区数量，可以基于处理器数量或其他条件
	const unsigned int buffer_number = max(processor_count / 2, 2u);

	std::vector<std::vector<path_match_struct>> path_vector_buffer(buffer_number);
	unsigned int path_vector_count = 0;

	Concurrency::concurrent_vector<search_result*> matched_path_vector;

	const auto max_path_vector_size = max(processor_count, 4u) * 256u;
	for (unsigned int i = 0; i < buffer_number; ++i)
	{
		path_vector_buffer[i].reserve(max_path_vector_size);
	}

	unsigned int active_buffer = 0;

	std::vector<BS::multi_future<void>> buffer_future(buffer_number);

	while (!query_done)
	{
		switch (sqlite3_step(query_stmt))
		{
		case SQLITE_ROW:
		{
			const char* parent_path_var = reinterpret_cast<const char*>(sqlite3_column_text(query_stmt, 0));
			const char* file_name_var = reinterpret_cast<const char*>(sqlite3_column_text(query_stmt, 1));

			// check string length greater than MAX_PATH_LENGTH
			if (strlen(parent_path_var) >= MAX_PATH_LENGTH || strlen(file_name_var) >= MAX_PATH_LENGTH) [[unlikely]]
			{
				continue;
			}

			const auto file_size_var = sqlite3_column_int64(query_stmt, 2);
			const auto modify_date = sqlite3_column_int64(query_stmt, 3);

			//new path match struct and add to path vector
			path_match_struct tmp;
			strcpy_s(tmp.parent_path_var, parent_path_var);
			strcpy_s(tmp.file_name_var, file_name_var);
			tmp.file_size = static_cast<const DWORD>(file_size_var);
			tmp.modify_time = static_cast<const uint64_t>(modify_date);

			path_vector_buffer[active_buffer].emplace_back(tmp);
			++path_vector_count;

			//if path vector size >= max_path_vector_size
			if (path_vector_count >= max_path_vector_size)
			{
				buffer_future[active_buffer] = submit_search_task(path_vector_count, path_vector_buffer[active_buffer], info_pointer, matched_path_vector);

				// switch buffer using modulo operation
				active_buffer = (active_buffer + 1) % buffer_number;
				buffer_future[active_buffer].wait();
				path_vector_buffer[active_buffer].clear();
				path_vector_count = 0;
			}

			if (info_pointer->result_counter.load() > max_result_unsigned)
			{
				query_done = true;
			}
		}
		break;
		case SQLITE_DONE:
			query_done = true;
			break;
		default:
			query_done = true;
			fprintf(stderr, "query failed, sql: %s\n", sql_string.data());
			fflush(stderr);
			break;
		}
	}
	for (unsigned int i = 0; i < buffer_number; ++i)
	{
		if (buffer_future[i].valid())
		{
			buffer_future[i].wait();
		}
	}

	if (query_stmt != nullptr)
	{
		sqlite3_finalize(query_stmt);
	}

	// check if there's still not enough results
	if (info_pointer->result_counter.load() < max_result_unsigned)
	{
		if (!path_vector_buffer[active_buffer].empty())
		{
			auto&& task_future = submit_search_task(
				static_cast<const unsigned>(path_vector_buffer[active_buffer].size()),
				path_vector_buffer[active_buffer], info_pointer, matched_path_vector);
			task_future.wait();
		}
	}

	if (!matched_path_vector.empty())
	{
		init_search_result_class(env, result_consumer);
		// Check java class and methods
		if (g_search_result_class == nullptr || g_search_result_constructor == nullptr ||
			g_search_result_consumer_class == nullptr || g_search_result_accept_func == nullptr) [[unlikely]]
		{
			return;
		}

		for (auto&& result : matched_path_vector)
		{
			const long long time_since_epoch = result->modify_date.time_since_epoch() / one_second;

			const auto path_jstring = env->NewStringUTF(result->path.c_str());
			const auto result_obj = env->NewObject(g_search_result_class, g_search_result_constructor,
				path_jstring,
				static_cast<jlong>(time_since_epoch),
				static_cast<jlong>(result->file_size),
				result->fuzzy_matched);

			env->CallVoidMethod(result_consumer, g_search_result_accept_func, result_obj);

			env->DeleteLocalRef(result_obj);
			env->DeleteLocalRef(path_jstring);
		}

		for (auto&& result : matched_path_vector)
		{
			delete result;
		}
	}
}

BS::multi_future<void> submit_search_task(const unsigned int path_vector_count,
	const std::vector<path_match_struct>& path_vector,
	search_info* search_info_ptr,
	Concurrency::concurrent_vector<search_result*>& matched_path_vector)
{
	//start path match
	return search_thread_pool.submit_loop<size_t>(0U,
		path_vector_count,
		[&path_vector, &search_info_ptr, &matched_path_vector](const size_t task_index)
		{
			auto&& path_to_match = path_vector[task_index];
			is_path_matched_keywords(
				path_to_match,
				search_info_ptr,
				matched_path_vector
			);
		});
}

/*
 * Class:     file_engine_dllInterface_PathMatcher
 * Method:    openConnections
 * Signature: ([Ljava/lang/String;)I
 */
JNIEXPORT jlong JNICALL Java_file_engine_dllInterface_PathMatcher_openConnections
(JNIEnv* env, jobject, jobjectArray db_str_arr)
{
	Connection_t db_connection;

	const auto arr_len = env->GetArrayLength(db_str_arr);
	for (jsize i = 0; i < arr_len; ++i)
	{
		const auto db_path_obj = env->GetObjectArrayElement(db_str_arr, i);
		const auto db_path_jstring = reinterpret_cast<jstring>(db_path_obj);
		const auto db_path_str = env->GetStringUTFChars(db_path_jstring, nullptr);
		const std::string db_path_string(db_path_str);
		env->ReleaseStringUTFChars(db_path_jstring, db_path_str);

		std::vector<sqlite3*> db_arr;

		// 为每个连接ID创建独立的数据库连接
		for (int j = 0; j < SQLITE_CONNECTION_NUMBER; ++j)
		{
			sqlite3* db = nullptr;
			const auto rc = sqlite3_open(db_path_string.data(), &db);
			if (rc != SQLITE_OK)
			{
				// 清理已经创建的连接
				for (int k = 0; k < j; ++k)
				{
					if (db_arr[k] != nullptr)
					{
						sqlite3_close(db_arr[k]);
					}
				}
				return 0;
			}

			// 配置数据库连接
			auto&& key = encrypt::getkey();
			std::string pragma_command = "PRAGMA key = '" + key + "';";
			sqlite3_exec(db, pragma_command.c_str(), nullptr, nullptr, nullptr);
			sqlite3_exec(db, "PRAGMA TEMP_STORE=MEMORY;", nullptr, nullptr, nullptr);
			sqlite3_exec(db, "PRAGMA auto_vacuum=0;", nullptr, nullptr, nullptr);
			sqlite3_exec(db, "PRAGMA mmap_size=134217728;", nullptr, nullptr, nullptr);

			db_arr.emplace_back(db);
		}

		db_connection.insert(std::make_pair(db_path_string, db_arr));
	}

	const auto current_connection_id = connection_id_generator++;
	{
		std::unique_lock ul(connection_rw_lock);
		connection_map.insert(std::make_pair(current_connection_id, db_connection));
	}
	return current_connection_id;
}

/*
 * Class:     file_engine_dllInterface_PathMatcher
 * Method:    closeConnections
 * Signature: (I)V
 */
JNIEXPORT void JNICALL Java_file_engine_dllInterface_PathMatcher_closeConnections
(JNIEnv*, jobject, jlong connection_id_to_close)
{
	std::unique_lock ul(connection_rw_lock);
	const auto iter = connection_map.find(static_cast<long>(connection_id_to_close));
	if (iter == connection_map.end()) [[unlikely]]
	{
		fprintf(stderr, "Connection ID %lld does not exist.\n", connection_id_to_close);
		fflush(stderr);
		return;
	}

	for (const auto& db_connections = iter->second;
		const auto& vals : db_connections)
	{
		// 关闭该数据库路径的所有连接
		for (int i = 0; i < SQLITE_CONNECTION_NUMBER; ++i)
		{
			if (vals.second[i] != nullptr)
			{
				sqlite3_close(vals.second[i]);
			}
		}
	}
	connection_map.erase(iter);
}

/*
 * Class:     file_engine_dllInterface_PathMatcher
 * Method:    iterateUwpApps
 * Signature: (Ljava/util/function/Consumer;)V
 */
JNIEXPORT void JNICALL Java_file_engine_dllInterface_PathMatcher_iterateUwpApps
(JNIEnv* env, jobject, jobject uwp_result_consumer)
{
	init_uwp_result_class(env, uwp_result_consumer);
	const auto& uwp_vector = get_all_uwp();
	for (const auto& each_uwp : uwp_vector)
	{
		const jstring displayName = wstring2jstring(env, each_uwp.DisplayName);
		const jstring name = wstring2jstring(env, each_uwp.Name);
		const jstring version = wstring2jstring(env, each_uwp.Version);
		const jint architecture = each_uwp.Architecture;
		const jstring resourceId = wstring2jstring(env, each_uwp.ResourceId);
		const jstring publisher = wstring2jstring(env, each_uwp.Publisher);
		const jstring publisherId = wstring2jstring(env, each_uwp.PublisherId);
		const jstring fullName = wstring2jstring(env, each_uwp.FullName);
		const jstring familyName = wstring2jstring(env, each_uwp.FamilyName);
		const jstring installLocation = wstring2jstring(env, each_uwp.InstallLocation);
		const jstring appUserModelId = wstring2jstring(env, each_uwp.AppUserModelId);

		const auto result_obj = env->NewObject(g_uwp_result_class, g_uwp_result_constructor,
			displayName,
			name,
			version,
			architecture,
			resourceId,
			publisher,
			publisherId,
			fullName,
			familyName,
			installLocation,
			appUserModelId
		);

		env->CallVoidMethod(uwp_result_consumer, g_uwp_consumer_accept_func, result_obj);

		env->DeleteLocalRef(result_obj);
		env->DeleteLocalRef(displayName);
		env->DeleteLocalRef(name);
		env->DeleteLocalRef(version);
		env->DeleteLocalRef(resourceId);
		env->DeleteLocalRef(publisher);
		env->DeleteLocalRef(publisherId);
		env->DeleteLocalRef(fullName);
		env->DeleteLocalRef(familyName);
		env->DeleteLocalRef(installLocation);
	}
}

/*
 * Class:     file_engine_dllInterface_PathMatcher
 * Method:    refreshUwpApps
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_file_engine_dllInterface_PathMatcher_refreshUwpApps
(JNIEnv*, jobject)
{
	refresh_uwp_cache();
}

/*
 * Class:     file_engine_dllInterface_PathMatcher
 * Method:    cleanupUwpApps
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_file_engine_dllInterface_PathMatcher_cleanupUwpApps
(JNIEnv*, jobject)
{
	cleanup_uwp_cache();
}

jstring wstring2jstring(JNIEnv* env, const std::wstring& wstr)
{
	return env->NewString(reinterpret_cast<const jchar*>(wstr.c_str()), static_cast<jsize>(wstr.length()));
}

void init_uwp_result_class(JNIEnv* env, jobject uwp_result_consumer)
{
	std::call_once(java_uwp_class_flag, [env, uwp_result_consumer]
		{
			// find uwp result class
			const auto uwp_result_class = env->FindClass("file/engine/entity/UwpResult");
			if (uwp_result_class == nullptr)
			{
				env->ThrowNew(env->FindClass("java/lang/Exception"), "Failed to obtain UwpResult class.");
				return;
			}
			g_uwp_result_class = (jclass)env->NewGlobalRef(uwp_result_class);
			env->DeleteLocalRef(uwp_result_class);

			// find uwp result constructor
			g_uwp_result_constructor = env->GetMethodID(g_uwp_result_class, "<init>",
				"(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V");
			if (g_uwp_result_constructor == nullptr)
			{
				env->ThrowNew(env->FindClass("java/lang/Exception"), "Failed to obtain UwpResult class constructor.");
				return;
			}

			// find Consumer interface
			const auto consumer_class = env->GetObjectClass(uwp_result_consumer);
			if (consumer_class == nullptr)
			{
				env->ThrowNew(env->FindClass("java/lang/Exception"), "Failed to obtain Consumer class.");
				return;
			}
			g_uwp_consumer_class = (jclass)env->NewGlobalRef(consumer_class);
			env->DeleteLocalRef(consumer_class);

			g_uwp_consumer_accept_func = env->GetMethodID(g_uwp_consumer_class, "accept", "(Ljava/lang/Object;)V");
			if (g_uwp_consumer_accept_func == nullptr)
			{
				env->ThrowNew(env->FindClass("java/lang/Exception"), "Failed to obtain Consumer class accept function.");
			}
		}
	);
}

void init_search_result_class(JNIEnv* env, jobject result_consumer)
{
	std::call_once(java_class_flag, [env, result_consumer]
		{
			// find SearchResult class
			const auto search_result_class = env->FindClass("file/engine/entity/SearchResult");
			if (search_result_class == nullptr)
			{
				env->ThrowNew(env->FindClass("java/lang/Exception"), "Failed to obtain SearchResult class.");
				return;
			}
			g_search_result_class = (jclass)env->NewGlobalRef(search_result_class);
			env->DeleteLocalRef(search_result_class);

			// find constructor
			g_search_result_constructor = env->GetMethodID(g_search_result_class, "<init>",
				"(Ljava/lang/String;JJZ)V");
			if (g_search_result_constructor == nullptr)
			{
				env->ThrowNew(env->FindClass("java/lang/Exception"), "Failed to obtain SearchResult class constructor.");
				return;
			}

			// find Consumer interface
			const auto consumer_class = env->GetObjectClass(result_consumer);
			if (consumer_class == nullptr)
			{
				env->ThrowNew(env->FindClass("java/lang/Exception"), "Failed to obtain Consumer class.");
				return;
			}
			g_search_result_consumer_class = (jclass)env->NewGlobalRef(consumer_class);
			env->DeleteLocalRef(consumer_class);

			g_search_result_accept_func = env->GetMethodID(g_search_result_consumer_class, "accept", "(Ljava/lang/Object;)V");
			if (g_search_result_accept_func == nullptr)
			{
				env->ThrowNew(env->FindClass("java/lang/Exception"), "Failed to obtain Consumer class accept function.");
			}
		});
}
