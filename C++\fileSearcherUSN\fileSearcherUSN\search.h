﻿#pragma once

#include <string>
#include <Windows.h>
#include "hash_table8.hpp"
#include "sqlite3.h"
#include <shared_mutex>
#include "BS_thread_pool.hpp"

#define CONCURRENT_MAP concurrency::concurrent_unordered_map
#define CONCURRENT_SET concurrency::concurrent_unordered_set

typedef struct record_struct_
{
    int ascii = 0;
    unsigned long long parent_path_id = 0;
    std::string file_name;
    LONGLONG file_size = 0;
    DWORD file_attribute = INVALID_FILE_ATTRIBUTES;
    LONGLONG modify_time = 0;
} RECORD_STRUCT;

typedef struct pfrn_name_
{
    DWORDLONG pfrn = 0;
    std::string filename;
    LONGLONG modify_time = 0;
    DWORD file_attribute = INVALID_FILE_ATTRIBUTES;
} PFRN_NAME;

typedef emhash8::HashMap<std::string, int> PriorityMap;
typedef emhash8::HashMap<DWORDLONG, PFRN_NAME> Frn_Pfrn_Name_Map;

class volume
{
public:
    volume(char vol, sqlite3* database, std::vector<std::string>* ignore_paths, PriorityMap* priority_map);

    volume(volume&) = delete;

    volume(volume&&) = delete;

    ~volume();

    char get_disk_path() const
    {
        return vol;
    }

    void collect_result_to_result_map(const RECORD_STRUCT& record_struct);

    void init_volume();

private:
    char vol;
    unsigned long long idGenerator = 0;
    BS::thread_pool index_thread_pool;
    HANDLE hVol;
    Frn_Pfrn_Name_Map frnPfrnNameMap;
    emhash8::HashMap<std::string, DWORDLONG> parentPathIdMap;
    std::vector<std::string> parentPathCacheVec;
    emhash8::HashMap<std::string, unsigned long long> parentPathCacheIndexMap;
    std::shared_mutex parentPathMutex;
    sqlite3* db;
    sqlite3_stmt* stmt0 = nullptr;
    sqlite3_stmt* stmt1 = nullptr;
    sqlite3_stmt* stmt2 = nullptr;
    sqlite3_stmt* stmt3 = nullptr;
    sqlite3_stmt* stmt4 = nullptr;
    sqlite3_stmt* stmt5 = nullptr;
    sqlite3_stmt* stmt6 = nullptr;
    sqlite3_stmt* stmt7 = nullptr;
    sqlite3_stmt* stmt8 = nullptr;
    sqlite3_stmt* stmt9 = nullptr;
    sqlite3_stmt* stmt10 = nullptr;
    sqlite3_stmt* stmt11 = nullptr;
    sqlite3_stmt* stmt12 = nullptr;
    sqlite3_stmt* stmt13 = nullptr;
    sqlite3_stmt* stmt14 = nullptr;
    sqlite3_stmt* stmt15 = nullptr;
    sqlite3_stmt* stmt16 = nullptr;
    sqlite3_stmt* stmt17 = nullptr;
    sqlite3_stmt* stmt18 = nullptr;
    sqlite3_stmt* stmt19 = nullptr;
    sqlite3_stmt* stmt20 = nullptr;
    sqlite3_stmt* stmt21 = nullptr;
    sqlite3_stmt* stmt22 = nullptr;
    sqlite3_stmt* stmt23 = nullptr;
    sqlite3_stmt* stmt24 = nullptr;
    sqlite3_stmt* stmt25 = nullptr;
    sqlite3_stmt* stmt26 = nullptr;
    sqlite3_stmt* stmt27 = nullptr;
    sqlite3_stmt* stmt28 = nullptr;
    sqlite3_stmt* stmt29 = nullptr;
    sqlite3_stmt* stmt30 = nullptr;
    sqlite3_stmt* stmt31 = nullptr;
    sqlite3_stmt* stmt32 = nullptr;
    sqlite3_stmt* stmt33 = nullptr;
    sqlite3_stmt* stmt34 = nullptr;
    sqlite3_stmt* stmt35 = nullptr;
    sqlite3_stmt* stmt36 = nullptr;
    sqlite3_stmt* stmt37 = nullptr;
    sqlite3_stmt* stmt38 = nullptr;
    sqlite3_stmt* stmt39 = nullptr;
    sqlite3_stmt* stmt40 = nullptr;
    sqlite3_stmt* stmt_folder = nullptr;

    USN_JOURNAL_DATA ujd{};

    std::vector<std::string>* ignore_path_vector_ = nullptr;
    PriorityMap* priority_map_ = nullptr;

    bool get_handle();
    void save_result(const std::string_view& parent_path, const std::string& file_name, int ascii, int ascii_group,
                     int priority, LONGLONG file_size, LONGLONG modify_time);
    void get_path(DWORDLONG frn, std::string& output_path);
    static int get_asc_ii_sum(const std::string& name);
    bool is_ignore(const std::string& path) const;
    void finalize_all_statement() const;
    void save_single_record_to_db(sqlite3_stmt* stmt, const std::string_view& parent_path, const std::string& file_name,
                                  int ascii, int priority, LONGLONG file_size, LONGLONG modify_time);
    int get_priority_by_suffix(const std::string& suffix) const;
    int get_priority_by_name(DWORD file_attribute, const std::string& file_name) const;
    void init_all_prepare_statement();
    void init_single_prepare_statement(sqlite3_stmt** statement, const char* init) const;
    bool create_usn() const;
    bool get_usn_info();
    bool get_usn_journal();
    bool delete_usn() const;
    void read_parent_path_id();
    unsigned long long get_parent_path_cache_id(const std::string& parent_path);
    std::string_view get_path_by_cache_id(unsigned long long cache_id);
};


std::string to_utf8(const wchar_t* buffer, int len);

std::string to_utf8(const std::wstring& str);

std::string get_file_name(const std::string& path);

std::string get_parent_path(const std::string& path);

LONGLONG get_file_time(const std::wstring& file_path);

LONGLONG decode_file_time(const FILETIME& ft);

LONGLONG get_file_size(const std::wstring& path);

bool get_file_attributes(const std::wstring& path, LPWIN32_FILE_ATTRIBUTE_DATA p_file_attr_data);

bool is_file_exist(const std::wstring& path);

uint64_t timeSinceEpochMillisec();
