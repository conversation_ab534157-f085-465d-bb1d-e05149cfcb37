package file.engine.utils.llm.impl;

import com.google.gson.Gson;
import file.engine.entity.Args;
import file.engine.utils.JsonExtractUtil;
import file.engine.utils.RegexUtil;
import file.engine.utils.gson.GsonUtil;
import file.engine.utils.llm.LLMInterface;
import io.github.ollama4j.OllamaAPI;
import io.github.ollama4j.exceptions.OllamaBaseException;
import io.github.ollama4j.models.chat.OllamaChatMessage;
import io.github.ollama4j.models.chat.OllamaChatMessageRole;
import io.github.ollama4j.models.chat.OllamaChatRequestBuilder;
import io.github.ollama4j.models.chat.OllamaChatResult;
import io.github.ollama4j.models.generate.OllamaStreamHandler;
import io.github.ollama4j.tools.Tools;
import lombok.Getter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.regex.Pattern;

@Slf4j
public class OllamaLLM implements LLMInterface {
    @Getter
    private final String address;
    @Getter
    private final String apiKey;
    private OllamaAPI ollamaAPI;
    private final LinkedHashMap<String, Tools.ToolSpecification> tools = new LinkedHashMap<>();
    private final ConcurrentHashMap<String, List<OllamaChatMessage>> historyMap = new ConcurrentHashMap<>();
    @Getter
    private final String llmModelType;

    public OllamaLLM(String address, String modelType, String apiKey) {
        this.address = address;
        this.apiKey = apiKey;
        this.llmModelType = modelType;
    }

    @Override
    public void init() {
        this.ollamaAPI = new OllamaAPI(address);
        if (this.apiKey != null && !this.apiKey.isEmpty()) {
            this.ollamaAPI.setBearerAuth(apiKey);
        }
        this.ollamaAPI.setRequestTimeoutSeconds(5 * 60);
    }

    @Override
    public String newSession() {
        if (this.ollamaAPI == null || !ollamaAPI.ping()) {
            return "";
        }
        UUID uuid = UUID.randomUUID();
        String uuidStr = uuid.toString();
        historyMap.put(uuidStr, new ArrayList<>());
        return uuidStr;
    }

    @Override
    public void removeSession(String sessionId) {
        historyMap.remove(sessionId);
    }

    @Override
    public String chat(String sessionId, String message) {
        if (this.ollamaAPI == null || !ollamaAPI.ping()) {
            return "";
        }
        var ollamaChatRequestBuilder = OllamaChatRequestBuilder.getInstance(llmModelType);
        List<OllamaChatMessage> chatHistory = historyMap.get(sessionId);
        OllamaChatResult chatResult;
        try {
            if (chatHistory == null || chatHistory.isEmpty()) {
                chatResult = ollamaAPI.chat(ollamaChatRequestBuilder.withMessage(OllamaChatMessageRole.USER, message).build());
            } else {
                chatResult = ollamaAPI.chat(ollamaChatRequestBuilder.withMessages(chatHistory).withMessage(OllamaChatMessageRole.USER, message).build());
            }
            historyMap.put(sessionId, chatResult.getChatHistory());
            return chatResult.getResponse();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void chat(String sessionId, String message, OllamaStreamHandler streamHandler) {
        if (this.ollamaAPI == null || !ollamaAPI.ping()) {
            return;
        }
        var ollamaChatRequestBuilder = OllamaChatRequestBuilder.getInstance(llmModelType);
        List<OllamaChatMessage> chatHistory = historyMap.get(sessionId);
        OllamaChatResult chatResult;
        try {
            if (chatHistory == null || chatHistory.isEmpty()) {
                chatResult = ollamaAPI.chat(ollamaChatRequestBuilder.withMessage(OllamaChatMessageRole.USER, message).build(), streamHandler);
            } else {
                chatResult = ollamaAPI.chat(ollamaChatRequestBuilder.withMessages(chatHistory).withMessage(OllamaChatMessageRole.USER, message).build(), streamHandler);
            }
            historyMap.put(sessionId, chatResult.getChatHistory());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @SneakyThrows
    @Override
    public String chatWithTools(String sessionId, String message, boolean[] toolsCallDone) {
        if (this.ollamaAPI == null || !ollamaAPI.ping()) {
            return "";
        }
        Gson gson = GsonUtil.INSTANCE.getGson();
        var ollamaChatRequestBuilder = OllamaChatRequestBuilder.getInstance(llmModelType);

        List<OllamaChatMessage> chatHistory = historyMap.get(sessionId);
        if (chatHistory == null || chatHistory.isEmpty()) {
            var promptBuilder = new StringBuilder()
                    .append("你是一个使用工具进行文件搜索的专家，当用户输入搜索请求时，你将会把调用的方法名和参数列出，并输出为JSON结构。\n")
                    .append("注意，你输出的JSON结构必须是可以解析的，一定要严格遵守JSON的格式!!!!!!\n")
                    .append("不要在输出中添加任何多余的字符或者注释!!!!!!\n")
                    .append("当用户的请求比较复杂，你需要使用多个工具时，" +
                            "在工具调用的JSON结构里设置一个done参数，将其设置为false，在下次请求时我将会附上当前请求返回的结果，并再次请求你，" +
                            "直到你认为所有的操作都已完成，将done设置为true，我就会结束对话，然后将最后的结果返回给用户。\n")
                    .append("注意：只有用户在请求中，明确提出了\"然后\"，\"并且\"，\"且\"，\"接下来\"，\"之后\"等等这样的词汇，你才能把done设置为false，代表你在下次请求仍然需要调用工具实现之后的需求。否则直接将done设置为true\n")
                    .append("例子：\n")
                    .append("当用户输入\"我想搜索文件名中包含 test 的文件或文件夹\"时，你的应该调用search方法，参数searchText为 test\n")
                    .append("由于用户只有一个搜索的需求，没有后续任务，所以done设置为true\n")
                    .append("在绝大多数情况下，done都是true，不是false，因为用户一般只会发出一个搜索请求，并不会提出其他的需求\n")
                    .append("所以你的输出为：\n")
                    .append("""
                            {
                              "functionName": "search",
                              "parameters": {
                                "searchText": { "type": "string", "description": "关键字", "value": "test", "required": true }
                              }
                              "done": true
                            }
                            """)
                    .append("如果用户输入\"我想搜索文件名中包含 test 的文件或文件夹并打开搜索出的第一个文件\"时，你应该调用search和open方法，参数searchText为 test，done为false")
                    .append("所以你的输出为：\n")
                    .append("""
                            {
                              "functionName": "search",
                              "parameters": {
                                "searchText": { "type": "string", "description": "关键字", "value": "test", "required": true }
                              }
                              "done": false
                            }
                            """)
                    .append("随后我将执行工具，并将返回结果发送给你，例如：\n")
                    .append("""
                            {
                               "data: [
                                    "Apps": [
                                        "C:\\path\\to\\file.exe"
                                        .......
                                    ]
                               ]
                            }
                            """)
                    .append("此时你将根据刚才发送给你的返回结果，调用open工具，打开第一个文件")
                    .append("所以你的输出为：\n")
                    .append("""
                            {
                              "functionName": "open",
                              "parameters": {
                                "path": { "type": "string", "description": "文件路径", "value": "C:\\path\\to\\file.exe", "required": true }
                              }
                              "done": true
                            }
                            """)
                    .append("如果你认为返回结果已经可以直接返回给用户了，不需要再调用工具，则你直接返回一个JSON结果\n")
                    .append("""
                            {
                                "done": true
                            }
                            """)
                    .append("代表不需要进行下一步操作，这样结果就会直接返回给用户。\n")
                    .append("当方法介绍中参数的required属性为true时，你必须在parameters中添加该参数\n")
                    .append("下面我将给出你可以使用的所有工具的列表，并给出使用的场景和提示\n");
            for (Map.Entry<String, Tools.ToolSpecification> entry : tools.entrySet()) {
                String key = entry.getKey();
                Tools.ToolSpecification value = entry.getValue();
                promptBuilder.append("方法名为：").append(key).append("\n").append("使用提示：").append(value.getFunctionDescription()).append("\n")
                        .append("当调用该函数时你该输出的JSON结构：\n");
                LinkedHashMap<String, Object> functionJson = new LinkedHashMap<>();
                functionJson.put("functionName", value.getFunctionName());

                LinkedHashMap<String, Object> paramsMap = new LinkedHashMap<>();
                functionJson.put("parameters", paramsMap);

                functionJson.put("done", "true或者false");

                var functionDefinition = value.getToolPrompt().getFunction();
                var parameters = functionDefinition.getParameters();
                Map<String, Tools.PromptFuncDefinition.Property> properties = parameters.getProperties();
                properties.forEach((argName, argProp) -> {
                    LinkedHashMap<String, Object> paramPropMap = new LinkedHashMap<>();
                    paramPropMap.put("type", argProp.getType());
                    paramPropMap.put("description", argProp.getDescription());
                    paramPropMap.put("value", "请在这里把" + argName + "对应的参数值替换");
                    paramsMap.put(argName, paramPropMap);
                });
                String functionJsonStr = gson.toJson(functionJson);
                promptBuilder.append(functionJsonStr).append("\n");
            }
            promptBuilder.append("再次提醒!!!!!你的输出只能是JSON结构的字符串，并且要直接能转换为Map。也不要添加任何markdown格式的类似于```的标记\n" +
                                 "直接输出JSON字符串!!!!!!\n" +
                                 "DO NOT ADD ADDITIONAL CHARACTERS!!!!，" +
                                 "注意！！！你需要特别注意，当用户需要匹配正则表达式或者用户输入的关键字中有转义字符 \\ 时，你需要保证输出的JSON仍然有效，一定不能出现输出的字符串中只有一个 \\ 字符的情况" +
                                 "所有的JSON字符串中的\\都应该被转换为\\\\，不能出现奇数个的\\字符!!\n" +
                                 "你的输出必须如下所示: \n")
                    .append("""
                            {
                              "functionName": "替换为方法名",
                              "parameters": {
                                "替换为方法定义的参数名": { "type": "替换为参数类型", "description": "替换为参数description", "value": "替换为参数值" }
                              },
                              "done": true或者false
                            }
                            """)
                    .append("如果你理解了，那么下面你就直接输出一个JSON结构，里面只有一个字段 done 结果为true。")
                    .append("""
                            {
                              "done" : true
                            }
                            """);
            var requestModel = ollamaChatRequestBuilder.withMessage(OllamaChatMessageRole.SYSTEM, promptBuilder.toString())
                    .build();
            try {
                var initialChatResult = ollamaAPI.chat(requestModel);
                chatHistory = initialChatResult.getChatHistory();
                historyMap.put(sessionId, chatHistory);
            } catch (OllamaBaseException | IOException | InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
        try {
            Pattern thinkReplace = RegexUtil.getPattern("<think>.*?</think>", Pattern.DOTALL);
            OllamaChatResult chatResult = ollamaAPI.chat(ollamaChatRequestBuilder.withMessages(chatHistory).withMessage(OllamaChatMessageRole.USER,
                    message).build());
            chatHistory = chatResult.getChatHistory();
            historyMap.put(sessionId, chatHistory);

            String response = chatResult.getResponse();
            // 过滤掉 <think></think> 标签
            response = thinkReplace.matcher(response).replaceAll("").trim(); // 使用正则表达式替换

            String doubleCheck = "```" + response + "```\n" +
                                 "这是你刚才的输出，请你再次检查这个结构是否能从JSON解析为一个Map。\n" +
                                 "如果你认为上一次我发送给你的结果已经可以直接展示给用户了，那么请原封不动的根据我上次发送给你的结果输出出来，不要更改任何一个字段。\n" +
                                 "如果你认为还需要执行操作，那么请同时确认是否需要多步操作才能完成用户请求。如果你确认上面的输出无误，那么重新输出一次上面的JSON结果。\n" +
                                 "否则，输出一个新的JSON结构。比如你发现其实并不需要多步操作才能完成请求，只需要一次方法的调用即可，那么你把done从false改成true，再次输出。或者你发现你调用了错误的方法，实际应该调用另一个方法，" +
                                 "则重新输出对应方法的JSON结构。";
            chatResult = ollamaAPI.chat(ollamaChatRequestBuilder.withMessages(chatHistory).withMessage(OllamaChatMessageRole.USER,
                    doubleCheck).build());
            chatHistory = chatResult.getChatHistory();
            historyMap.put(sessionId, chatHistory);
            response = chatResult.getResponse();
            // 过滤掉 <think></think> 标签
            response = thinkReplace.matcher(response).replaceAll("").trim(); // 使用正则表达式替换

            String jsonExtracted = JsonExtractUtil.extractJsonString(response);

            Map responseMap = gson.fromJson(jsonExtracted, Map.class);
            String functionName = (String) responseMap.get("functionName");
            Map<String, Object> parameters = (Map<String, Object>) responseMap.get("parameters");
            boolean done = Boolean.parseBoolean(String.valueOf(responseMap.get("done")));
            if (toolsCallDone != null && toolsCallDone.length == 1) {
                toolsCallDone[0] = done;
            }
            Tools.ToolSpecification toolSpecification = tools.get(functionName);
            if (toolSpecification == null) {
                return "";
            }
            if (parameters == null) {
                return "";
            }
            HashMap<String, Object> paramWithValue = new HashMap<>();
            parameters.forEach((k, v) -> {
                if (v instanceof Map) {
                    paramWithValue.put(k, ((Map<?, ?>) v).get("value"));
                }
            });
            return (String) toolSpecification.getToolFunction().apply(paramWithValue);
        } catch (OllamaBaseException | IOException | InterruptedException e) {
            log.error("error {}", e.getMessage(), e);
            return "";
        }
    }

    @SneakyThrows
    @Override
    public String chatWithImage(String sessionId, String message, List<File> images) {
        if (this.ollamaAPI == null || !ollamaAPI.ping()) {
            return "";
        }
        var ollamaChatRequestBuilder = OllamaChatRequestBuilder.getInstance(llmModelType);
        try {
            List<OllamaChatMessage> chatHistory = historyMap.get(sessionId);
            OllamaChatResult chatResult;
            if (chatHistory != null && !chatHistory.isEmpty()) {
                chatResult = ollamaAPI.chat(ollamaChatRequestBuilder.withMessages(chatHistory)
                        .withMessage(OllamaChatMessageRole.USER,
                                message,
                                Collections.emptyList(),
                                images).build());
            } else {
                chatResult = ollamaAPI.chat(ollamaChatRequestBuilder.withMessage(OllamaChatMessageRole.USER,
                        message,
                        Collections.emptyList(),
                        images).build());
            }
            chatHistory = chatResult.getChatHistory();
            historyMap.put(sessionId, chatHistory);
            Pattern thinkReplace = RegexUtil.getPattern("<think>.*?</think>", Pattern.DOTALL);
            String response = chatResult.getResponse();
            return thinkReplace.matcher(response).replaceAll("").trim(); // 使用正则表达式替换
        } catch (OllamaBaseException | IOException | InterruptedException e) {
            log.error("error {}", e.getMessage(), e);
        }
        return "";
    }

    @Override
    public void registerApi(String apiName,
                            String apiFunctionName,
                            String apiDescription,
                            String apiVersion,
                            List<Args> args,
                            Function<Map<String, Object>, String> func) {
        if (this.ollamaAPI == null || !ollamaAPI.ping()) {
            return;
        }
        var descriptionBuilder = Tools.PromptFuncDefinition.PromptFuncSpec.builder()
                .name(apiName)
                .description(apiDescription);

        Tools.PropsBuilder propsBuilder = new Tools.PropsBuilder();
        ArrayList<String> requiredArgList = new ArrayList<>();
        for (Args arg : args) {
            propsBuilder.withProperty(arg.name(), Tools.PromptFuncDefinition.Property.builder().type(arg.type())
                    .description(arg.description()).required(arg.required()).build());
            if (arg.required()) {
                requiredArgList.add(arg.name());
            }
        }
        descriptionBuilder.parameters(Tools.PromptFuncDefinition.Parameters.builder()
                .type("object")
                .properties(propsBuilder.build())
                .required(requiredArgList)
                .build());

        Tools.ToolSpecification tool = Tools.ToolSpecification.builder()
                .functionName(apiFunctionName)
                .functionDescription(apiDescription)
                .toolPrompt(Tools.PromptFuncDefinition.builder()
                        .type("function")
                        .function(descriptionBuilder.build()).build())
                .toolFunction(func::apply)
                .build();
        tools.put(apiFunctionName, tool);
    }
}
