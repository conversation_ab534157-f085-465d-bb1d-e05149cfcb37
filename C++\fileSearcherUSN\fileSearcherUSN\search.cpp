﻿#include "constants.h"
#include "search.h"
#ifdef TEST
#include <iostream>
#endif
#include <algorithm>
#include <ranges>
#include <string>
#include <vector>
#include "sqlite3.h"
#include <concurrent_unordered_set.h>
#include <filesystem>
#include "string_to_utf8.h"
#include "concurrent_queue.h"
#include <thread>
#include <atomic>
#include <Windows.h>

volume::volume(const char vol, sqlite3* database, std::vector<std::string>* ignore_paths, PriorityMap* priority_map)
{
    this->vol = vol;
    this->priority_map_ = priority_map;
    hVol = nullptr;
    db = database;
    ignore_path_vector_ = ignore_paths;
}

volume::~volume()
{
    CloseHandle(hVol);
}

void volume::read_parent_path_id()
{
    // read folder table and put to parentPathIdMap
    sqlite3_stmt* query_folder_stmt;
    const char* sql_str = "select ID, PATH from folder;";
    const int rc = sqlite3_prepare_v2(db, sql_str, -1, &query_folder_stmt, nullptr);
    if (rc != SQLITE_OK)
    {
        (void)fprintf(stderr, "prepare sql failed, sql: %s\n", sql_str);
        (void)fflush(stderr);
        return;
    }
    bool query_done = false;
    while (!query_done)
    {
        switch (sqlite3_step(query_folder_stmt))
        {
        case SQLITE_ROW:
            {
                const auto folder_id = sqlite3_column_int64(query_folder_stmt, 0);
                const auto path_var = sqlite3_column_text(query_folder_stmt, 1);
                std::string folder_path_str(reinterpret_cast<const char*>(path_var));
                parentPathIdMap.emplace_unique(folder_path_str, folder_id);
            }
            break;
        case SQLITE_DONE:
            query_done = true;
            break;
        default:
            query_done = true;
            (void)fprintf(stderr, "query failed, sql: %s\n", sql_str);
            (void)fflush(stderr);
            break;
        }
    }
    sqlite3_finalize(query_folder_stmt);
}

std::string_view volume::get_path_by_cache_id(const unsigned long long cache_id)
{
    std::shared_lock slck(this->parentPathMutex);
    const auto& parent_path_string = parentPathCacheVec[cache_id];
    return parent_path_string;
}

unsigned long long volume::get_parent_path_cache_id(const std::string& parent_path)
{
    unsigned long long parent_path_id_find = 0;
    bool found = false;

    {
        std::shared_lock slck(this->parentPathMutex);
        const auto& parent_path_iter = parentPathCacheIndexMap.find(parent_path);
        if (parent_path_iter != parentPathCacheIndexMap.end())
        {
            parent_path_id_find = parent_path_iter->second;
            found = true;
        }
    }

    if (!found)
    {
        std::unique_lock ulck(this->parentPathMutex);

        parent_path_id_find = idGenerator;
        parentPathCacheVec.emplace_back(parent_path);
        parentPathCacheIndexMap.emplace_unique(parent_path, parent_path_id_find);
        ++idGenerator;
    }
    return parent_path_id_find;
}

void volume::init_volume()
{
    if (get_handle() && create_usn() && get_usn_info() && get_usn_journal())
    {
        using namespace std;

        read_parent_path_id();
        const auto search_internal_async = [this]
        {
            concurrency::concurrent_queue<RECORD_STRUCT> working_queue;
            std::atomic_bool is_producer_exit(false);
            std::thread producer_thread([this, &working_queue, &is_producer_exit]
            {
                try
                {
                    auto&& start_iter = frnPfrnNameMap.begin();
                    const auto& end_iter = frnPfrnNameMap.end();

                    short count = 0;
                    while (start_iter != end_iter)
                    {
                        ++count;

                        const unsigned long long parent_start_id = start_iter->first;

                        const auto& name = start_iter->second.filename;
                        const auto& file_attr = start_iter->second.file_attribute;
                        const auto& modify_time = start_iter->second.modify_time;

                        const bool check_exist = count > 5;
                        if (check_exist)
                        {
                            count = 0;
                        }

                        (void)index_thread_pool.submit_task(
                            [
                                name,
                                check_exist,
                                file_attr,
                                modify_time,
                                &end_iter,
                                &working_queue,
                                parent_start_id,
                                this
                            ]
                            {
                                std::string result_path;
                                get_path(parent_start_id, result_path);
                                const auto record = vol + result_path;
                                if (!is_ignore(record))
                                {
                                    bool file_exist = true;

                                    const int ascii = get_asc_ii_sum(name);
                                    const auto& parent_path_str = get_parent_path(record);
                                    const auto& file_name_str = get_file_name(record);

                                    const auto& parent_path_id_find = get_parent_path_cache_id(parent_path_str);

                                    RECORD_STRUCT record_struct;
                                    record_struct.ascii = ascii;
                                    record_struct.file_attribute = file_attr;
                                    record_struct.parent_path_id = parent_path_id_find;
                                    record_struct.file_name = file_name_str;
                                    record_struct.modify_time = modify_time;

                                    if (!check_exist || is_file_exist(string2wstring(record)))
                                    {
                                        working_queue.push(record_struct);
                                    }
                                    else
                                    {
                                        file_exist = false;
                                    }

                                    if (file_exist)
                                    {
                                        if (std::string parent_path = get_parent_path(record); !parent_path.empty())
                                        {
                                            parent_path = parent_path.substr(0, parent_path.length() - 1);

                                            if (!parent_path.empty() && parent_path != std::to_string(vol) + ":")
                                            {
                                                const auto& parent_path_name = get_file_name(parent_path);
                                                const auto& ancestor_path = get_parent_path(parent_path);
                                                const auto& ancestor_path_id = get_parent_path_cache_id(ancestor_path);

                                                RECORD_STRUCT parent_record_struct;
                                                parent_record_struct.ascii = get_asc_ii_sum(parent_path_name);
                                                parent_record_struct.file_attribute = FILE_ATTRIBUTE_DIRECTORY;
                                                parent_record_struct.parent_path_id = ancestor_path_id;
                                                parent_record_struct.file_name = parent_path_name;
                                                parent_record_struct.modify_time = modify_time;

                                                working_queue.push(parent_record_struct);
                                            }
                                        }
                                    }
                                }
                            });

                        ++start_iter;
                    }

                    index_thread_pool.wait();
                }
                catch (std::exception& e)
                {
                    (void)fprintf(stderr, "fileSearcherUSN: error: %s", e.what());
                }
                is_producer_exit.store(true);
            });

            std::thread consumer_thread([this, &working_queue, &is_producer_exit]
            {
                unsigned int count = 0;
                init_all_prepare_statement();
                while (!is_producer_exit.load() || !working_queue.empty())
                {
                    if (RECORD_STRUCT record_struct; working_queue.try_pop(record_struct))
                    {
                        ++count;
                        collect_result_to_result_map(record_struct);

                        if (count > SAVE_TO_DATABASE_RECORD_CHECKPOINT)
                        {
                            count = 0;
                            finalize_all_statement();
                            init_all_prepare_statement();
                        }
                    }
                    else
                    {
                        std::this_thread::yield();
                    }
                }
                finalize_all_statement();
            });

            if (producer_thread.joinable())
            {
                producer_thread.join();
            }
            if (consumer_thread.joinable())
            {
                consumer_thread.join();
            }
        };
        try
        {
            printf("start to collect disk %c info.\n", this->get_disk_path());
            auto&& start_time = timeSinceEpochMillisec();
            search_internal_async();
            auto&& end_time = timeSinceEpochMillisec();
            printf("collect disk %c complete. time in mills: %llu\n", this->get_disk_path(), end_time - start_time);
        }
        catch (exception& e)
        {
            (void)fprintf(stderr, "fileSearcherUSN: %s\n", e.what());
        }
    }
    else
    {
        (void)fprintf(stderr, "fileSearcherUSN: init usn journal failed.\n");
    }
    auto&& info = std::string("disk ") + this->get_disk_path() + " complete";
    printf("%s\n", info.c_str());
}

void volume::collect_result_to_result_map(const RECORD_STRUCT& record_struct)
{
    const int ascii_group = record_struct.ascii / 100;
    const auto ascii_group_real = min(ascii_group, MAX_TABLE_NUM);
    const int priority = get_priority_by_name(record_struct.file_attribute, record_struct.file_name);
    const auto& parent_path = get_path_by_cache_id(record_struct.parent_path_id);

    save_result(parent_path, record_struct.file_name, record_struct.ascii, ascii_group_real, priority,
                record_struct.file_size, record_struct.modify_time);
}

int volume::get_priority_by_suffix(const std::string& suffix) const
{
    auto&& iter = priority_map_->find(suffix);
    if (iter == priority_map_->end())
    {
        return get_priority_by_suffix("defaultPriority");
    }
    return iter->second;
}


int volume::get_priority_by_name(const DWORD file_attribute, const std::string& file_name) const
{
    if (file_attribute != INVALID_FILE_ATTRIBUTES && file_attribute & FILE_ATTRIBUTE_DIRECTORY)
    {
        return get_priority_by_suffix("dirPriority");
    }
    auto&& suffix = file_name.substr(file_name.find_last_of('.') + 1);
    std::ranges::transform(suffix, suffix.begin(), tolower);
    return get_priority_by_suffix(suffix);
}

void volume::init_single_prepare_statement(sqlite3_stmt** statement, const char* init) const
{
    if (const size_t ret = sqlite3_prepare_v2(db, init, static_cast<long>(strlen(init)), statement, nullptr);
        SQLITE_OK != ret)
    {
        auto&& err_info = std::string("error preparing stmt \"") + init + "\"  disk: " + this->get_disk_path();
        (void)fprintf(stderr, "fileSearcherUSN: %s\n", err_info.c_str());
    }
}

void volume::finalize_all_statement() const
{
    sqlite3_finalize(stmt0);
    sqlite3_finalize(stmt1);
    sqlite3_finalize(stmt2);
    sqlite3_finalize(stmt3);
    sqlite3_finalize(stmt4);
    sqlite3_finalize(stmt5);
    sqlite3_finalize(stmt6);
    sqlite3_finalize(stmt7);
    sqlite3_finalize(stmt8);
    sqlite3_finalize(stmt9);
    sqlite3_finalize(stmt10);
    sqlite3_finalize(stmt11);
    sqlite3_finalize(stmt12);
    sqlite3_finalize(stmt13);
    sqlite3_finalize(stmt14);
    sqlite3_finalize(stmt15);
    sqlite3_finalize(stmt16);
    sqlite3_finalize(stmt17);
    sqlite3_finalize(stmt18);
    sqlite3_finalize(stmt19);
    sqlite3_finalize(stmt20);
    sqlite3_finalize(stmt21);
    sqlite3_finalize(stmt22);
    sqlite3_finalize(stmt23);
    sqlite3_finalize(stmt24);
    sqlite3_finalize(stmt25);
    sqlite3_finalize(stmt26);
    sqlite3_finalize(stmt27);
    sqlite3_finalize(stmt28);
    sqlite3_finalize(stmt29);
    sqlite3_finalize(stmt30);
    sqlite3_finalize(stmt31);
    sqlite3_finalize(stmt32);
    sqlite3_finalize(stmt33);
    sqlite3_finalize(stmt34);
    sqlite3_finalize(stmt35);
    sqlite3_finalize(stmt36);
    sqlite3_finalize(stmt37);
    sqlite3_finalize(stmt38);
    sqlite3_finalize(stmt39);
    sqlite3_finalize(stmt40);
    sqlite3_finalize(stmt_folder);
    sqlite3_exec(db, "commit;", nullptr, nullptr, nullptr);
    sqlite3_mutex_leave(sqlite3_db_mutex(db));
}

void volume::save_single_record_to_db(sqlite3_stmt* stmt, const std::string_view& parent_path_view,
                                      const std::string& file_name,
                                      const int ascii,
                                      const int priority, const LONGLONG file_size, const LONGLONG modify_time)
{
    if (parent_path_view.empty() || file_name.empty())
    {
        return;
    }

    const std::string parent_path(parent_path_view);

    DWORDLONG folder_id;
    // check if id already exists
    if (auto&& parent_path_iter = parentPathIdMap.find(parent_path);
        parent_path_iter == parentPathIdMap.end())
    {
        // generate new id and put to map
        sqlite3_reset(stmt_folder);
        sqlite3_bind_text(stmt_folder, 1, parent_path.c_str(), -1, SQLITE_STATIC);
        if (const auto rc = sqlite3_step(stmt_folder); rc == SQLITE_ROW)
        {
            folder_id = sqlite3_column_int64(stmt_folder, 0);
        }
        parentPathIdMap.emplace_unique(parent_path, folder_id);
    }
    else
    {
        // use existing id
        folder_id = parent_path_iter->second;
    }

    sqlite3_reset(stmt);
    sqlite3_bind_int(stmt, 1, ascii);
    sqlite3_bind_text(stmt, 2, file_name.c_str(), -1, SQLITE_STATIC);
    sqlite3_bind_int64(stmt, 3, file_size);
    sqlite3_bind_int64(stmt, 4, modify_time);
    sqlite3_bind_int(stmt, 5, priority);
    sqlite3_bind_int64(stmt, 6, static_cast<sqlite3_int64>(folder_id));
    sqlite3_step(stmt);
}

void volume::init_all_prepare_statement()
{
    sqlite3_mutex_enter(sqlite3_db_mutex(db));
    sqlite3_exec(db, "begin;", nullptr, nullptr, nullptr);
    init_single_prepare_statement(
        &stmt0,
        "INSERT OR IGNORE INTO list0(ASCII, NAME, FILE_SIZE, MODIFY_DATE, PRIORITY, FOLDER_ID) VALUES(?, ?, ?, ?, ?, ?);");
    init_single_prepare_statement(
        &stmt1,
        "INSERT OR IGNORE INTO list1(ASCII, NAME, FILE_SIZE, MODIFY_DATE, PRIORITY, FOLDER_ID) VALUES(?, ?, ?, ?, ?, ?);");
    init_single_prepare_statement(
        &stmt2,
        "INSERT OR IGNORE INTO list2(ASCII, NAME, FILE_SIZE, MODIFY_DATE, PRIORITY, FOLDER_ID) VALUES(?, ?, ?, ?, ?, ?);");
    init_single_prepare_statement(
        &stmt3,
        "INSERT OR IGNORE INTO list3(ASCII, NAME, FILE_SIZE, MODIFY_DATE, PRIORITY, FOLDER_ID) VALUES(?, ?, ?, ?, ?, ?);");
    init_single_prepare_statement(
        &stmt4,
        "INSERT OR IGNORE INTO list4(ASCII, NAME, FILE_SIZE, MODIFY_DATE, PRIORITY, FOLDER_ID) VALUES(?, ?, ?, ?, ?, ?);");
    init_single_prepare_statement(
        &stmt5,
        "INSERT OR IGNORE INTO list5(ASCII, NAME, FILE_SIZE, MODIFY_DATE, PRIORITY, FOLDER_ID) VALUES(?, ?, ?, ?, ?, ?);");
    init_single_prepare_statement(
        &stmt6,
        "INSERT OR IGNORE INTO list6(ASCII, NAME, FILE_SIZE, MODIFY_DATE, PRIORITY, FOLDER_ID) VALUES(?, ?, ?, ?, ?, ?);");
    init_single_prepare_statement(
        &stmt7,
        "INSERT OR IGNORE INTO list7(ASCII, NAME, FILE_SIZE, MODIFY_DATE, PRIORITY, FOLDER_ID) VALUES(?, ?, ?, ?, ?, ?);");
    init_single_prepare_statement(
        &stmt8,
        "INSERT OR IGNORE INTO list8(ASCII, NAME, FILE_SIZE, MODIFY_DATE, PRIORITY, FOLDER_ID) VALUES(?, ?, ?, ?, ?, ?);");
    init_single_prepare_statement(
        &stmt9,
        "INSERT OR IGNORE INTO list9(ASCII, NAME, FILE_SIZE, MODIFY_DATE, PRIORITY, FOLDER_ID) VALUES(?, ?, ?, ?, ?, ?);");
    init_single_prepare_statement(
        &stmt10,
        "INSERT OR IGNORE INTO list10(ASCII, NAME, FILE_SIZE, MODIFY_DATE, PRIORITY, FOLDER_ID) VALUES(?, ?, ?, ?, ?, ?);");
    init_single_prepare_statement(
        &stmt11,
        "INSERT OR IGNORE INTO list11(ASCII, NAME, FILE_SIZE, MODIFY_DATE, PRIORITY, FOLDER_ID) VALUES(?, ?, ?, ?, ?, ?);");
    init_single_prepare_statement(
        &stmt12,
        "INSERT OR IGNORE INTO list12(ASCII, NAME, FILE_SIZE, MODIFY_DATE, PRIORITY, FOLDER_ID) VALUES(?, ?, ?, ?, ?, ?);");
    init_single_prepare_statement(
        &stmt13,
        "INSERT OR IGNORE INTO list13(ASCII, NAME, FILE_SIZE, MODIFY_DATE, PRIORITY, FOLDER_ID) VALUES(?, ?, ?, ?, ?, ?);");
    init_single_prepare_statement(
        &stmt14,
        "INSERT OR IGNORE INTO list14(ASCII, NAME, FILE_SIZE, MODIFY_DATE, PRIORITY, FOLDER_ID) VALUES(?, ?, ?, ?, ?, ?);");
    init_single_prepare_statement(
        &stmt15,
        "INSERT OR IGNORE INTO list15(ASCII, NAME, FILE_SIZE, MODIFY_DATE, PRIORITY, FOLDER_ID) VALUES(?, ?, ?, ?, ?, ?);");
    init_single_prepare_statement(
        &stmt16,
        "INSERT OR IGNORE INTO list16(ASCII, NAME, FILE_SIZE, MODIFY_DATE, PRIORITY, FOLDER_ID) VALUES(?, ?, ?, ?, ?, ?);");
    init_single_prepare_statement(
        &stmt17,
        "INSERT OR IGNORE INTO list17(ASCII, NAME, FILE_SIZE, MODIFY_DATE, PRIORITY, FOLDER_ID) VALUES(?, ?, ?, ?, ?, ?);");
    init_single_prepare_statement(
        &stmt18,
        "INSERT OR IGNORE INTO list18(ASCII, NAME, FILE_SIZE, MODIFY_DATE, PRIORITY, FOLDER_ID) VALUES(?, ?, ?, ?, ?, ?);");
    init_single_prepare_statement(
        &stmt19,
        "INSERT OR IGNORE INTO list19(ASCII, NAME, FILE_SIZE, MODIFY_DATE, PRIORITY, FOLDER_ID) VALUES(?, ?, ?, ?, ?, ?);");
    init_single_prepare_statement(
        &stmt20,
        "INSERT OR IGNORE INTO list20(ASCII, NAME, FILE_SIZE, MODIFY_DATE, PRIORITY, FOLDER_ID) VALUES(?, ?, ?, ?, ?, ?);");
    init_single_prepare_statement(
        &stmt21,
        "INSERT OR IGNORE INTO list21(ASCII, NAME, FILE_SIZE, MODIFY_DATE, PRIORITY, FOLDER_ID) VALUES(?, ?, ?, ?, ?, ?);");
    init_single_prepare_statement(
        &stmt22,
        "INSERT OR IGNORE INTO list22(ASCII, NAME, FILE_SIZE, MODIFY_DATE, PRIORITY, FOLDER_ID) VALUES(?, ?, ?, ?, ?, ?);");
    init_single_prepare_statement(
        &stmt23,
        "INSERT OR IGNORE INTO list23(ASCII, NAME, FILE_SIZE, MODIFY_DATE, PRIORITY, FOLDER_ID) VALUES(?, ?, ?, ?, ?, ?);");
    init_single_prepare_statement(
        &stmt24,
        "INSERT OR IGNORE INTO list24(ASCII, NAME, FILE_SIZE, MODIFY_DATE, PRIORITY, FOLDER_ID) VALUES(?, ?, ?, ?, ?, ?);");
    init_single_prepare_statement(
        &stmt25,
        "INSERT OR IGNORE INTO list25(ASCII, NAME, FILE_SIZE, MODIFY_DATE, PRIORITY, FOLDER_ID) VALUES(?, ?, ?, ?, ?, ?);");
    init_single_prepare_statement(
        &stmt26,
        "INSERT OR IGNORE INTO list26(ASCII, NAME, FILE_SIZE, MODIFY_DATE, PRIORITY, FOLDER_ID) VALUES(?, ?, ?, ?, ?, ?);");
    init_single_prepare_statement(
        &stmt27,
        "INSERT OR IGNORE INTO list27(ASCII, NAME, FILE_SIZE, MODIFY_DATE, PRIORITY, FOLDER_ID) VALUES(?, ?, ?, ?, ?, ?);");
    init_single_prepare_statement(
        &stmt28,
        "INSERT OR IGNORE INTO list28(ASCII, NAME, FILE_SIZE, MODIFY_DATE, PRIORITY, FOLDER_ID) VALUES(?, ?, ?, ?, ?, ?);");
    init_single_prepare_statement(
        &stmt29,
        "INSERT OR IGNORE INTO list29(ASCII, NAME, FILE_SIZE, MODIFY_DATE, PRIORITY, FOLDER_ID) VALUES(?, ?, ?, ?, ?, ?);");
    init_single_prepare_statement(
        &stmt30,
        "INSERT OR IGNORE INTO list30(ASCII, NAME, FILE_SIZE, MODIFY_DATE, PRIORITY, FOLDER_ID) VALUES(?, ?, ?, ?, ?, ?);");
    init_single_prepare_statement(
        &stmt31,
        "INSERT OR IGNORE INTO list31(ASCII, NAME, FILE_SIZE, MODIFY_DATE, PRIORITY, FOLDER_ID) VALUES(?, ?, ?, ?, ?, ?);");
    init_single_prepare_statement(
        &stmt32,
        "INSERT OR IGNORE INTO list32(ASCII, NAME, FILE_SIZE, MODIFY_DATE, PRIORITY, FOLDER_ID) VALUES(?, ?, ?, ?, ?, ?);");
    init_single_prepare_statement(
        &stmt33,
        "INSERT OR IGNORE INTO list33(ASCII, NAME, FILE_SIZE, MODIFY_DATE, PRIORITY, FOLDER_ID) VALUES(?, ?, ?, ?, ?, ?);");
    init_single_prepare_statement(
        &stmt34,
        "INSERT OR IGNORE INTO list34(ASCII, NAME, FILE_SIZE, MODIFY_DATE, PRIORITY, FOLDER_ID) VALUES(?, ?, ?, ?, ?, ?);");
    init_single_prepare_statement(
        &stmt35,
        "INSERT OR IGNORE INTO list35(ASCII, NAME, FILE_SIZE, MODIFY_DATE, PRIORITY, FOLDER_ID) VALUES(?, ?, ?, ?, ?, ?);");
    init_single_prepare_statement(
        &stmt36,
        "INSERT OR IGNORE INTO list36(ASCII, NAME, FILE_SIZE, MODIFY_DATE, PRIORITY, FOLDER_ID) VALUES(?, ?, ?, ?, ?, ?);");
    init_single_prepare_statement(
        &stmt37,
        "INSERT OR IGNORE INTO list37(ASCII, NAME, FILE_SIZE, MODIFY_DATE, PRIORITY, FOLDER_ID) VALUES(?, ?, ?, ?, ?, ?);");
    init_single_prepare_statement(
        &stmt38,
        "INSERT OR IGNORE INTO list38(ASCII, NAME, FILE_SIZE, MODIFY_DATE, PRIORITY, FOLDER_ID) VALUES(?, ?, ?, ?, ?, ?);");
    init_single_prepare_statement(
        &stmt39,
        "INSERT OR IGNORE INTO list39(ASCII, NAME, FILE_SIZE, MODIFY_DATE, PRIORITY, FOLDER_ID) VALUES(?, ?, ?, ?, ?, ?);");
    init_single_prepare_statement(
        &stmt40,
        "INSERT OR IGNORE INTO list40(ASCII, NAME, FILE_SIZE, MODIFY_DATE, PRIORITY, FOLDER_ID) VALUES(?, ?, ?, ?, ?, ?);");
    init_single_prepare_statement(
        &stmt_folder,
        "insert into folder(PATH) values(?) on CONFLICT(PATH) do update set PATH=excluded.PATH RETURNING ID;");
}

bool volume::is_ignore(const std::string& _path) const
{
    if (_path.find('$') != std::string::npos)
    {
        return true;
    }
    std::string path0(_path);
    std::ranges::transform(path0, path0.begin(), tolower);
    return std::ranges::any_of(*ignore_path_vector_, [&path0](const std::string& each)
    {
        return path0.find(each) != std::string::npos;
    });
}

void volume::save_result(const std::string_view& parent_path, const std::string& file_name, const int ascii,
                         const int ascii_group, const int priority,
                         const LONGLONG file_size, const LONGLONG modify_time)
{
    switch (ascii_group)
    {
    case 0:
        save_single_record_to_db(stmt0, parent_path, file_name, ascii, priority, file_size, modify_time);
        break;
    case 1:
        save_single_record_to_db(stmt1, parent_path, file_name, ascii, priority, file_size, modify_time);
        break;
    case 2:
        save_single_record_to_db(stmt2, parent_path, file_name, ascii, priority, file_size, modify_time);
        break;
    case 3:
        save_single_record_to_db(stmt3, parent_path, file_name, ascii, priority, file_size, modify_time);
        break;
    case 4:
        save_single_record_to_db(stmt4, parent_path, file_name, ascii, priority, file_size, modify_time);
        break;
    case 5:
        save_single_record_to_db(stmt5, parent_path, file_name, ascii, priority, file_size, modify_time);
        break;
    case 6:
        save_single_record_to_db(stmt6, parent_path, file_name, ascii, priority, file_size, modify_time);
        break;
    case 7:
        save_single_record_to_db(stmt7, parent_path, file_name, ascii, priority, file_size, modify_time);
        break;
    case 8:
        save_single_record_to_db(stmt8, parent_path, file_name, ascii, priority, file_size, modify_time);
        break;
    case 9:
        save_single_record_to_db(stmt9, parent_path, file_name, ascii, priority, file_size, modify_time);
        break;
    case 10:
        save_single_record_to_db(stmt10, parent_path, file_name, ascii, priority, file_size, modify_time);
        break;
    case 11:
        save_single_record_to_db(stmt11, parent_path, file_name, ascii, priority, file_size, modify_time);
        break;
    case 12:
        save_single_record_to_db(stmt12, parent_path, file_name, ascii, priority, file_size, modify_time);
        break;
    case 13:
        save_single_record_to_db(stmt13, parent_path, file_name, ascii, priority, file_size, modify_time);
        break;
    case 14:
        save_single_record_to_db(stmt14, parent_path, file_name, ascii, priority, file_size, modify_time);
        break;
    case 15:
        save_single_record_to_db(stmt15, parent_path, file_name, ascii, priority, file_size, modify_time);
        break;
    case 16:
        save_single_record_to_db(stmt16, parent_path, file_name, ascii, priority, file_size, modify_time);
        break;
    case 17:
        save_single_record_to_db(stmt17, parent_path, file_name, ascii, priority, file_size, modify_time);
        break;
    case 18:
        save_single_record_to_db(stmt18, parent_path, file_name, ascii, priority, file_size, modify_time);
        break;
    case 19:
        save_single_record_to_db(stmt19, parent_path, file_name, ascii, priority, file_size, modify_time);
        break;
    case 20:
        save_single_record_to_db(stmt20, parent_path, file_name, ascii, priority, file_size, modify_time);
        break;
    case 21:
        save_single_record_to_db(stmt21, parent_path, file_name, ascii, priority, file_size, modify_time);
        break;
    case 22:
        save_single_record_to_db(stmt22, parent_path, file_name, ascii, priority, file_size, modify_time);
        break;
    case 23:
        save_single_record_to_db(stmt23, parent_path, file_name, ascii, priority, file_size, modify_time);
        break;
    case 24:
        save_single_record_to_db(stmt24, parent_path, file_name, ascii, priority, file_size, modify_time);
        break;
    case 25:
        save_single_record_to_db(stmt25, parent_path, file_name, ascii, priority, file_size, modify_time);
        break;
    case 26:
        save_single_record_to_db(stmt26, parent_path, file_name, ascii, priority, file_size, modify_time);
        break;
    case 27:
        save_single_record_to_db(stmt27, parent_path, file_name, ascii, priority, file_size, modify_time);
        break;
    case 28:
        save_single_record_to_db(stmt28, parent_path, file_name, ascii, priority, file_size, modify_time);
        break;
    case 29:
        save_single_record_to_db(stmt29, parent_path, file_name, ascii, priority, file_size, modify_time);
        break;
    case 30:
        save_single_record_to_db(stmt30, parent_path, file_name, ascii, priority, file_size, modify_time);
        break;
    case 31:
        save_single_record_to_db(stmt31, parent_path, file_name, ascii, priority, file_size, modify_time);
        break;
    case 32:
        save_single_record_to_db(stmt32, parent_path, file_name, ascii, priority, file_size, modify_time);
        break;
    case 33:
        save_single_record_to_db(stmt33, parent_path, file_name, ascii, priority, file_size, modify_time);
        break;
    case 34:
        save_single_record_to_db(stmt34, parent_path, file_name, ascii, priority, file_size, modify_time);
        break;
    case 35:
        save_single_record_to_db(stmt35, parent_path, file_name, ascii, priority, file_size, modify_time);
        break;
    case 36:
        save_single_record_to_db(stmt36, parent_path, file_name, ascii, priority, file_size, modify_time);
        break;
    case 37:
        save_single_record_to_db(stmt37, parent_path, file_name, ascii, priority, file_size, modify_time);
        break;
    case 38:
        save_single_record_to_db(stmt38, parent_path, file_name, ascii, priority, file_size, modify_time);
        break;
    case 39:
        save_single_record_to_db(stmt39, parent_path, file_name, ascii, priority, file_size, modify_time);
        break;
    case 40:
        save_single_record_to_db(stmt40, parent_path, file_name, ascii, priority, file_size, modify_time);
        break;
    default:
        break;
    }
}

int volume::get_asc_ii_sum(const std::string& name)
{
    auto sum = 0;
    const auto length = name.length();
    for (size_t i = 0; i < length; i++)
    {
        if (name[i] > 0)
        {
            sum += name[i];
        }
    }
    return sum;
}

void volume::get_path(DWORDLONG frn, std::string& output_path)
{
    std::vector<std::string> path_parts;
    const auto end = frnPfrnNameMap.end();
    while (true)
    {
        const auto it = frnPfrnNameMap.find(frn);
        if (it == end)
        {
            break;
        }
        path_parts.emplace_back(it->second.filename);
        frn = it->second.pfrn;
    }

    if (path_parts.empty())
    {
        output_path = ":";
    }
    else
    {
        // 构建路径（从根节点向子节点）
        output_path += ':';
        for (const auto& path_part : std::ranges::reverse_view(path_parts))
        {
            output_path += '\\';
            output_path += path_part;
        }
    }
}

bool volume::get_handle()
{
    // 为\\?\C:的形式
    std::wstring lp_file_name = L"\\\\?\\";
    lp_file_name += vol;
    lp_file_name += L":";

    hVol = CreateFile(lp_file_name.c_str(),
                      GENERIC_READ | GENERIC_WRITE, // 可以为0
                      FILE_SHARE_READ | FILE_SHARE_WRITE, // 必须包含有FILE_SHARE_WRITE
                      nullptr,
                      OPEN_EXISTING, // 必须包含OPEN_EXISTING, CREATE_ALWAYS可能会导致错误
                      0, // FILE_ATTRIBUTE_NORMAL可能会导致错误
                      nullptr);

    if (INVALID_HANDLE_VALUE != hVol)
    {
        return true;
    }
    auto&& info = std::wstring(L"create file handle failed. ") + lp_file_name +
        L"error code: " + std::to_wstring(GetLastError());
    (void)fprintf(stderr, "fileSearcherUSN: %ls", info.c_str());
    return false;
}

LONGLONG get_file_time(const std::wstring& file_path)
{
    HANDLE hFile = CreateFile(file_path.c_str(), GENERIC_READ, FILE_SHARE_READ, NULL,
                              OPEN_EXISTING, 0, NULL);
    if (hFile == INVALID_HANDLE_VALUE)
    {
        return 0;
    }

    FILETIME ftCreate, ftAccess, ftWrite;
    LONGLONG ret = 0;
    // Retrieve the file times for the file.
    if (GetFileTime(hFile, &ftCreate, &ftAccess, &ftWrite))
    {
        ret = decode_file_time(ftWrite);
    }
    CloseHandle(hFile);
    return ret;
}

LONGLONG decode_file_time(const FILETIME& ft)
{
    LARGE_INTEGER uli;
    uli.HighPart = ft.dwHighDateTime;
    uli.LowPart = ft.dwLowDateTime;

    return uli.QuadPart;
}

std::string get_file_name(const std::string& path)
{
    std::string file_name = path.substr(path.find_last_of('\\') + 1);
    return file_name;
}

std::string get_parent_path(const std::string& path)
{
    const size_t pos = path.find_last_of('\\');

    // 如果找不到，表示没有父路径，返回空字符串
    if (pos == std::string::npos)
    {
        return "";
    }

    // 如果最后一个分隔符位于开头，说明是根路径，返回根路径
    if (pos == 0)
    {
        return "";
    }

    // 返回父路径，去掉最后一个部分
    return path.substr(0, pos) + "\\";
}

bool volume::create_usn() const
{
    NTFS_VOLUME_DATA_BUFFER ntfsVolData;
    DWORD dwWritten = 0;

    if (DeviceIoControl(hVol,
                        FSCTL_GET_NTFS_VOLUME_DATA,
                        nullptr,
                        0,
                        &ntfsVolData,
                        sizeof(ntfsVolData),
                        &dwWritten,
                        nullptr))
    {
        return true;
    }
    auto&& info = std::string("create usn error. Disk: ") +
        vol + " Error code: " + std::to_string(GetLastError());
    (void)fprintf(stderr, "fileSearcherUSN: %s\n", info.c_str());
    return false;
}

bool volume::get_usn_info()
{
    DWORD br;
    if (DeviceIoControl(hVol, // handle to volume
                        FSCTL_QUERY_USN_JOURNAL, // dwIoControlCode
                        nullptr, // lpInBuffer
                        0, // nInBufferSize
                        &ujd, // output buffer
                        sizeof(ujd), // size of output buffer
                        &br, // number of bytes returned
                        nullptr) // OVERLAPPED structure
    )
    {
        return true;
    }
    auto&& info = "query usn error. Error code: " + std::to_string(vol) + std::to_string(GetLastError());
    (void)fprintf(stderr, "fileSearcherUSN: %s\n", info.c_str());
    return false;
}

bool volume::get_usn_journal()
{
    MFT_ENUM_DATA med;
    med.StartFileReferenceNumber = 0;
    med.LowUsn = 0;
    med.HighUsn = ujd.NextUsn;
    med.MinMajorVersion = 2;
    med.MaxMajorVersion = 2;

    constexpr auto BUF_LEN = sizeof(USN) * 0x100000; // 尽可能地大，提高效率;

    CHAR* buffer = new CHAR[BUF_LEN];
    DWORD usn_data_size;

    while (true)
    {
        memset(buffer, 0, BUF_LEN);
        if (0 == DeviceIoControl(hVol,
                                 FSCTL_ENUM_USN_DATA,
                                 &med,
                                 sizeof med,
                                 buffer,
                                 BUF_LEN,
                                 &usn_data_size,
                                 nullptr))
        {
            break;
        }
        DWORD dw_ret_bytes = usn_data_size - sizeof(USN);
        // 找到第一个 USN 记录
        auto usn_record = reinterpret_cast<PUSN_RECORD>(buffer + sizeof(USN));

        while (dw_ret_bytes > 0)
        {
            // 获取到的信息
            const std::wstring cfile_name(usn_record->FileName, usn_record->FileNameLength / 2);

            PFRN_NAME pfrn_name;
            pfrn_name.filename = to_utf8(cfile_name);
            pfrn_name.file_attribute = usn_record->FileAttributes;
            pfrn_name.pfrn = usn_record->ParentFileReferenceNumber;
            pfrn_name.modify_time = usn_record->TimeStamp.QuadPart;

            frnPfrnNameMap.emplace_unique(usn_record->FileReferenceNumber, pfrn_name);

            // 获取下一个记录
            const auto record_len = usn_record->RecordLength;
            dw_ret_bytes -= record_len;
            usn_record = reinterpret_cast<PUSN_RECORD>(reinterpret_cast<PCHAR>(usn_record) + record_len);
        }
        // 获取下一页数据
        med.StartFileReferenceNumber = *reinterpret_cast<DWORDLONG*>(buffer);
    }
    delete[] buffer;
    return true;
}

bool is_file_exist(const std::wstring& path)
{
    return GetFileAttributes(path.c_str()) != INVALID_FILE_ATTRIBUTES;
}

bool get_file_attributes(const std::wstring& path, LPWIN32_FILE_ATTRIBUTE_DATA p_file_attr_data)
{
    if (GetFileAttributesEx(path.c_str(), GetFileExInfoStandard, p_file_attr_data))
    {
        return true;
    }
    return false;
}

LONGLONG get_file_size(const std::wstring& path)
{
    LARGE_INTEGER file_size{};
    WIN32_FILE_ATTRIBUTE_DATA file_attr_data;
    if (GetFileAttributesEx(path.c_str(), GetFileExInfoStandard, &file_attr_data))
    {
        file_size.LowPart = file_attr_data.nFileSizeLow;
        file_size.HighPart = file_attr_data.nFileSizeHigh;
    }
    return file_size.QuadPart;
}

bool volume::delete_usn() const
{
    DELETE_USN_JOURNAL_DATA dujd{ujd.UsnJournalID, USN_DELETE_FLAG_DELETE | USN_DELETE_FLAG_NOTIFY};
    DWORD br;

    if (DeviceIoControl(hVol,
                        FSCTL_DELETE_USN_JOURNAL,
                        &dujd,
                        sizeof(dujd),
                        nullptr,
                        0,
                        &br,
                        nullptr)
    )
    {
        return true;
    }
    return false;
}

uint64_t timeSinceEpochMillisec()
{
    using namespace std::chrono;
    return duration_cast<milliseconds>(system_clock::now().time_since_epoch()).count();
}
