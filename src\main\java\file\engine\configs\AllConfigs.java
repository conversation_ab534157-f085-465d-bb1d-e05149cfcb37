package file.engine.configs;

import file.engine.annotation.EventListener;
import file.engine.annotation.EventRegister;
import file.engine.dllInterface.GetWindowsKnownFolder;
import file.engine.dllInterface.IsLocalDisk;
import file.engine.dllInterface.gpu.GPUAccelerator;
import file.engine.entity.AdvancedConfigEntity;
import file.engine.entity.ConfigEntity;
import file.engine.event.handler.Event;
import file.engine.event.handler.EventManagement;
import file.engine.event.handler.impl.BootSystemEvent;
import file.engine.event.handler.impl.configs.CheckConfigsEvent;
import file.engine.event.handler.impl.configs.SetConfigsEvent;
import file.engine.event.handler.impl.stop.CloseEvent;
import file.engine.utils.RegexUtil;
import file.engine.utils.file.FileUtil;
import file.engine.utils.gson.GsonUtil;
import file.engine.utils.system.properties.IsDebug;
import io.github.ollama4j.types.OllamaModelType;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * 保存软件运行时的所有配置信息
 */
@Getter
@Slf4j
public class AllConfigs {
    private volatile ConfigEntity configEntity;
    private static volatile AllConfigs instance = null;
    @Getter
    private static String version = "0";
    @Getter
    private static String buildVersion = "0";

    private AllConfigs() {
        if (!IsDebug.isDebug) {
            /*
             * 读取maven自动生成的版本信息
             */
            Properties properties = new Properties();
            try (InputStream projectInfo = Constants.class.getResourceAsStream("/project-info.properties")) {
                properties.load(projectInfo);
                version = properties.getProperty("project.version");
                buildVersion = properties.getProperty("project.build.version");
            } catch (Exception e) {
                log.error("error: {}", e.getMessage(), e);
            }
        }
    }

    public static AllConfigs getInstance() {
        if (instance == null) {
            synchronized (AllConfigs.class) {
                if (instance == null) {
                    instance = new AllConfigs();
                }
            }
        }
        return instance;
    }

    /**
     * 获取在配置文件中但是实际不存在的磁盘（如移动硬盘）
     *
     * @return set
     */
    public Set<String> getUnAvailableDiskSet() {
        String disks = configEntity.getDisks();
        String[] splitDisks = RegexUtil.comma.split(disks);
        Set<String> set = ConcurrentHashMap.newKeySet();
        for (String root : splitDisks) {
            if (!isDiskAvailable(root)) {
                set.add(root);
            }
        }
        return set;
    }

    /**
     * 获取可搜索磁盘
     *
     * @return 每个磁盘使用逗号隔开，如[C:\,D:\]
     */
    public String getAvailableDisks() {
        String disks = configEntity.getDisks();
        String[] splitDisks = RegexUtil.comma.split(disks);
        StringBuilder stringBuilder = new StringBuilder();
        for (String root : splitDisks) {
            if (isDiskAvailable(root)) {
                stringBuilder.append(root).append(",");
            }
        }
        return stringBuilder.toString();
    }

    /**
     * 判断磁盘是否存在且为NTFS文件系统
     *
     * @param root 磁盘路径，C:\  D:\
     * @return true如果存在且是NTFS磁盘
     */
    public boolean isDiskAvailable(String root) {
        final boolean[] diskInfo = new boolean[]{false};
        CountDownLatch countDownLatch = new CountDownLatch(1);
        Thread start = Thread.ofPlatform().start(() -> {
            try {
                diskInfo[0] = IsLocalDisk.INSTANCE.isLocalDisk(root) && IsLocalDisk.INSTANCE.isDiskNTFS(root);
            } finally {
                countDownLatch.countDown();
            }
        });
        try {
            boolean await = countDownLatch.await(1, TimeUnit.SECONDS);
            if (!await) {
                log.warn("Warning: Get disk information timed out, disk: {}", root);
                start.interrupt();
            }
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        return FileUtil.isFileExist(root) && diskInfo[0];
    }

    /**
     * 获取所有可用的本地磁盘
     *
     * @return String，用逗号隔开
     */
    private String getLocalDisks() {
        File[] files = File.listRoots();
        if (files == null || files.length == 0) {
            return "";
        }
        String diskName;
        StringBuilder stringBuilder = new StringBuilder();
        for (File each : files) {
            diskName = each.getAbsolutePath();
            if (AllConfigs.getInstance().isDiskAvailable(diskName)) {
                stringBuilder.append(each.getAbsolutePath()).append(",");
            }
        }
        return stringBuilder.toString();
    }

    /**
     * 获取用户配置的磁盘信息
     *
     * @param settingsInJson 用户配置json
     */
    private void readDisks(Map<String, Object> settingsInJson) {
        String disks = getFromJson(settingsInJson, "disks", this::getLocalDisks);
        String[] stringDisk = RegexUtil.comma.split(disks);
        StringBuilder stringBuilder = new StringBuilder();
        for (String each : stringDisk) {
            stringBuilder.append(each).append(",");
        }
        configEntity.setDisks(stringBuilder.toString());
    }

    private void readIsEnableGpuAccelerate(Map<String, Object> settingsInJson) {
        boolean isEnableGpuAccelerate = getFromJson(settingsInJson, "isEnableGpuAccelerate", false);
        if (isEnableGpuAccelerate) {
            configEntity.setEnableGpuAccelerate(GPUAccelerator.INSTANCE.isGPUAvailableOnSystem());
        } else {
            configEntity.setEnableGpuAccelerate(false);
        }
    }

    private void readIsEnableFuzzyMatch(Map<String, Object> settingsInJson) {
        boolean isEnableFuzzyMatch = getFromJson(settingsInJson, "isEnableFuzzyMatch", true);
        configEntity.setEnableFuzzyMatch(isEnableFuzzyMatch);
    }

    private void readDataTypeMap(Map<String, Object> settingsInJson) {
        LinkedHashMap<String, List<String>> dataTypeSuffixMapDefault = new LinkedHashMap<>();
        // Shortcut
        List<String> suffixListShortcut = new ArrayList<>();
        suffixListShortcut.add("lnk");
        dataTypeSuffixMapDefault.put("Shortcut", suffixListShortcut);

        // App
        List<String> suffixListApp = new ArrayList<>();
        suffixListApp.add("exe");
        dataTypeSuffixMapDefault.put("App", suffixListApp);

        // Doc
        List<String> suffixListDoc = new ArrayList<>();
        suffixListDoc.add("txt");
        suffixListDoc.add("doc");
        suffixListDoc.add("docx");
        suffixListDoc.add("pdf");
        suffixListDoc.add("odt");
        suffixListDoc.add("rtf");
        suffixListDoc.add("md");
        suffixListDoc.add("tex");
        suffixListDoc.add("wks");
        suffixListDoc.add("wps");
        dataTypeSuffixMapDefault.put("Doc", suffixListDoc);

        // Sheet
        List<String> suffixListSheet = new ArrayList<>();
        suffixListSheet.add("xls");
        suffixListSheet.add("xlsx");
        suffixListSheet.add("csv");
        suffixListSheet.add("ods");
        suffixListSheet.add("tsv");
        suffixListSheet.add("xlsm");
        suffixListSheet.add("xltx");
        dataTypeSuffixMapDefault.put("Sheet", suffixListSheet);

        // Slide
        List<String> suffixListSlide = new ArrayList<>();
        suffixListSlide.add("ppt");
        suffixListSlide.add("pptx");
        suffixListSlide.add("odp");
        suffixListSlide.add("key");
        suffixListSlide.add("pps");
        suffixListSlide.add("pptm");
        dataTypeSuffixMapDefault.put("Slide", suffixListSlide);

        // Picture
        List<String> suffixListPicture = new ArrayList<>();
        suffixListPicture.add("jpg");
        suffixListPicture.add("jpeg");
        suffixListPicture.add("png");
        suffixListPicture.add("gif");
        suffixListPicture.add("bmp");
        suffixListPicture.add("tiff");
        suffixListPicture.add("svg");
        suffixListPicture.add("psd");
        suffixListPicture.add("ai");
        suffixListPicture.add("ico");
        suffixListPicture.add("eps");
        suffixListPicture.add("raw");
        dataTypeSuffixMapDefault.put("Picture", suffixListPicture);

        // Video
        List<String> suffixListVideo = new ArrayList<>();
        suffixListVideo.add("mp4");
        suffixListVideo.add("avi");
        suffixListVideo.add("mkv");
        suffixListVideo.add("mov");
        suffixListVideo.add("wmv");
        suffixListVideo.add("flv");
        suffixListVideo.add("mpeg");
        suffixListVideo.add("mpg");
        suffixListVideo.add("webm");
        suffixListVideo.add("vob");
        dataTypeSuffixMapDefault.put("Video", suffixListVideo);

        // Audio
        List<String> suffixListAudio = new ArrayList<>();
        suffixListAudio.add("mp3");
        suffixListAudio.add("wav");
        suffixListAudio.add("flac");
        suffixListAudio.add("aac");
        suffixListAudio.add("ogg");
        suffixListAudio.add("m4a");
        suffixListAudio.add("wma");
        suffixListAudio.add("aiff");
        suffixListAudio.add("mid");
        suffixListAudio.add("midi");
        dataTypeSuffixMapDefault.put("Audio", suffixListAudio);

        // Developer
        List<String> suffixListDeveloper = new ArrayList<>();
        suffixListDeveloper.add("c");
        suffixListDeveloper.add("h");
        suffixListDeveloper.add("cpp");
        suffixListDeveloper.add("hpp");
        suffixListDeveloper.add("java");
        suffixListDeveloper.add("class");
        suffixListDeveloper.add("jar");
        suffixListDeveloper.add("py");
        suffixListDeveloper.add("pyc");
        suffixListDeveloper.add("js");
        suffixListDeveloper.add("mjs");
        suffixListDeveloper.add("ts");
        suffixListDeveloper.add("tsx");
        suffixListDeveloper.add("html");
        suffixListDeveloper.add("css");
        suffixListDeveloper.add("scss");
        suffixListDeveloper.add("php");
        suffixListDeveloper.add("rb");
        suffixListDeveloper.add("go");
        suffixListDeveloper.add("swift");
        suffixListDeveloper.add("kt");
        suffixListDeveloper.add("kts");
        suffixListDeveloper.add("rs");
        suffixListDeveloper.add("r");
        suffixListDeveloper.add("rmd");
        suffixListDeveloper.add("lua");
        suffixListDeveloper.add("pl");
        suffixListDeveloper.add("pm");
        suffixListDeveloper.add("sh");
        suffixListDeveloper.add("bash");
        suffixListDeveloper.add("ps1");
        suffixListDeveloper.add("bat");
        suffixListDeveloper.add("cmd");
        suffixListDeveloper.add("sql");
        suffixListDeveloper.add("mat");
        suffixListDeveloper.add("hs");
        suffixListDeveloper.add("lhs");
        suffixListDeveloper.add("scala");
        suffixListDeveloper.add("dart");
        suffixListDeveloper.add("m");
        suffixListDeveloper.add("s");
        suffixListDeveloper.add("asm");
        suffixListDeveloper.add("groovy");
        suffixListDeveloper.add("yaml");
        suffixListDeveloper.add("json");
        suffixListDeveloper.add("xml");
        dataTypeSuffixMapDefault.put("Developer", suffixListDeveloper);
        Map<String, List<String>> dataTypeSuffixMap = getFromJson(settingsInJson, "dataTypeSuffixMap", dataTypeSuffixMapDefault);
        configEntity.setDataTypeSuffixMap(dataTypeSuffixMap);
    }

    private void readLLM(Map<String, Object> settingsInJson) {
        String llm = getFromJson(settingsInJson, "llm", Constants.Enums.LLM.NONE.name());
        configEntity.setLlm(llm);
    }

    private void readLLMConfigs(Map<String, Object> settingsInJson) {
        LinkedHashMap<String, Object> defaultLlmConfigMap = new LinkedHashMap<>();
        for (Constants.Enums.LLM llm : Constants.Enums.LLM.values()) {
            LinkedHashMap<String, Object> eachLlmConfig = getLlmDefaultConfigMap(llm);
            defaultLlmConfigMap.put(llm.name(), eachLlmConfig);
        }
        Map<String, Object> llmConfigs = getFromJson(settingsInJson, "llmConfigs", defaultLlmConfigMap);
        if (llmConfigs.isEmpty()) {
            configEntity.setLlmConfigs(defaultLlmConfigMap);
        } else {
            configEntity.setLlmConfigs(llmConfigs);
        }
    }

    @NotNull
    private static LinkedHashMap<String, Object> getLlmDefaultConfigMap(Constants.Enums.LLM llm) {
        LinkedHashMap<String, Object> eachLlmConfig = new LinkedHashMap<>();
        switch (llm) {
            case OLLAMA -> {
                eachLlmConfig.put("address", "http://localhost:11434");
                eachLlmConfig.put("modelType", OllamaModelType.QWEN2);
                eachLlmConfig.put("apiKey", "");
            }
        }
        return eachLlmConfig;
    }

    private void readGpuDevice(Map<String, Object> settingsInJson) {
        String deviceNumber = getFromJson(settingsInJson, "gpuDevice", "");
        Map<String, String> devices = GPUAccelerator.INSTANCE.getDevices();
        if (!deviceNumber.isEmpty() && devices.containsValue(deviceNumber)) {
            configEntity.setGpuDevice(deviceNumber);
        } else {
            configEntity.setGpuDevice("");
        }
    }

    private void readCacheNumLimit(Map<String, Object> settingsInJson) {
        configEntity.setCacheNumLimit(getFromJson(settingsInJson, "cacheNumLimit", 1000));
    }

    private void readPriorityFolder(Map<String, Object> settingsInJson) {
        configEntity.setPriorityFolder(getFromJson(settingsInJson, "priorityFolder", ""));
    }

    private void readIgnorePath(Map<String, Object> settingsInJson) {
        ArrayList<String> ignorePathList = new ArrayList<>();
        ignorePathList.add("C:\\Windows");
        ignorePathList.add(GetWindowsKnownFolder.INSTANCE.getKnownFolder("{AE50C081-EBD2-438A-8655-8A092E34987A}"));
        ignorePathList.add("C:\\Program Files\\WindowsApps");
        ignorePathList.add("C:\\ProgramData");
        var defaultIgnore = String.join(",", ignorePathList) + ",";
        configEntity.setIgnorePath(getFromJson(settingsInJson, "ignorePath", defaultIgnore));
    }

    private void readUpdateTimeLimit(Map<String, Object> settingsInJson) {
        configEntity.setUpdateTimeLimit(getFromJson(settingsInJson, "updateTimeLimit", 5));
    }

    @SuppressWarnings("unchecked")
    private AdvancedConfigEntity readAdvancedConfigs(Map<String, Object> settingsInJson) {
        Map<String, Object> advancedConfigs = (Map<String, Object>) settingsInJson.getOrDefault("advancedConfigs", new HashMap<String, Object>());
        long waitForSearchTasksTimeoutInMills = Long.parseLong(getFromJson(advancedConfigs, "waitForSearchTasksTimeoutInMills", (long) 5 * 60 * 1000).toString());
        boolean isDeleteUsnOnExit = Boolean.parseBoolean(getFromJson(advancedConfigs, "isDeleteUsnOnExit", false).toString());
        long restartMonitorDiskThreadTimeoutInMills = Long.parseLong(getFromJson(advancedConfigs, "restartMonitorDiskThreadTimeoutInMills", (long) 10 * 60 * 1000).toString());
        boolean isReadPictureByLLM = Boolean.parseBoolean(getFromJson(advancedConfigs, "isReadPictureByLLM", false).toString());
        boolean isEnableContentIndex = Boolean.parseBoolean(getFromJson(advancedConfigs, "isEnableContentIndex", false).toString());
        int minCacheBlockNumber = Integer.parseInt(getFromJson(advancedConfigs, "minCacheBlockNumber", 100).toString());
        int maxCacheBlockNumber = Integer.parseInt(getFromJson(advancedConfigs, "maxCacheBlockNumber", 5000).toString());
        int minGpuCacheBlockNumber = Integer.parseInt(getFromJson(advancedConfigs, "minGpuCacheBlockNumber", 3000).toString());
        int maxGpuCacheBlockNumber = Integer.parseInt(getFromJson(advancedConfigs, "maxGpuCacheBlockNumber", 20000).toString());

        return new AdvancedConfigEntity(
                waitForSearchTasksTimeoutInMills,
                isDeleteUsnOnExit,
                restartMonitorDiskThreadTimeoutInMills,
                isReadPictureByLLM,
                isEnableContentIndex,
                minCacheBlockNumber,
                maxCacheBlockNumber,
                minGpuCacheBlockNumber,
                maxGpuCacheBlockNumber);
    }

    private void readSearchThreadNumber(Map<String, Object> settingsInJson) {
        int maxThreadNumber = Runtime.getRuntime().availableProcessors() * 2;
        int searchThreadNumber = getFromJson(settingsInJson, "searchThreadNumber", maxThreadNumber);
        if (searchThreadNumber > maxThreadNumber || searchThreadNumber < 1) {
            searchThreadNumber = maxThreadNumber;
        }
        configEntity.setSearchThreadNumber(searchThreadNumber);
    }

    private String readConfigsJson0() {
        if (FileUtil.isFileExist(Constants.CONFIG_FILE)) {
            try {
                return Files.readString(Path.of(Constants.CONFIG_FILE), StandardCharsets.UTF_8);
            } catch (IOException e) {
                log.error(e.getMessage(), e);
            }
        }
        return "";
    }

    /**
     * 打开配置文件，解析为json
     *
     * @return JSON
     */
    @SuppressWarnings("unchecked")
    private Map<String, Object> getSettingsJSON() {
        String jsonConfig = readConfigsJson0();
        if (jsonConfig.isEmpty()) {
            return new HashMap<>();
        } else {
            return GsonUtil.INSTANCE.getGson().fromJson(jsonConfig, Map.class);
        }
    }

    /**
     * 尝试从json中读取，若失败则返回默认值
     *
     * @param json               json数据
     * @param key                key
     * @param defaultObjSupplier 默认值
     * @return 读取值或默认值
     */
    @SuppressWarnings("unchecked")
    private <T> T getFromJson(Map<String, Object> json, String key, Supplier<T> defaultObjSupplier) {
        if (json == null) {
            return defaultObjSupplier.get();
        }
        try {
            Object tmp = json.get(key);
            if (tmp == null) {
                if (IsDebug.isDebug) {
                    log.error("配置文件读取到null值   key : " + key);
                }
                return defaultObjSupplier.get();
            }
            return (T) tmp;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return defaultObjSupplier.get();
        }
    }

    /**
     * 尝试从json中读取，若失败则返回默认值
     *
     * @param json       json数据
     * @param key        key
     * @param defaultObj 默认值
     * @return 读取值或默认值
     */
    @SuppressWarnings("unchecked")
    private <T> T getFromJson(Map<String, Object> json, String key, Object defaultObj) {
        if (json == null) {
            return (T) defaultObj;
        }
        try {
            Object tmp = json.get(key);
            if (tmp == null) {
                if (IsDebug.isDebug) {
                    log.error("配置文件读取到null值   key : " + key);
                }
                return (T) defaultObj;
            }
            return (T) tmp;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return (T) defaultObj;
        }
    }

    /**
     * 检查配置并发出警告
     *
     * @param configEntity 配置
     * @return 错误信息
     */
    private static String checkPriorityFolder(ConfigEntity configEntity) {
        String priorityFolder = configEntity.getPriorityFolder();
        if (!priorityFolder.isEmpty() && !Files.exists(Path.of(priorityFolder))) {
            return "Priority folder does not exist";
        }
        return "";
    }

    /**
     * 读取所有配置
     */
    private void readAllSettings() {
        configEntity = new ConfigEntity();
        Map<String, Object> settingsInJson = getSettingsJSON();
        readUpdateTimeLimit(settingsInJson);
        readIgnorePath(settingsInJson);
        readPriorityFolder(settingsInJson);
        readCacheNumLimit(settingsInJson);
        readDisks(settingsInJson);
        readIsEnableGpuAccelerate(settingsInJson);
        readLLM(settingsInJson);
        readLLMConfigs(settingsInJson);
        readGpuDevice(settingsInJson);
        readSearchThreadNumber(settingsInJson);
        readDataTypeMap(settingsInJson);
        readIsEnableFuzzyMatch(settingsInJson);
        AdvancedConfigEntity advancedConfigEntity = readAdvancedConfigs(settingsInJson);
        configEntity.setAdvancedConfigEntity(advancedConfigEntity);
    }

    /**
     * 将配置保存到文件user/settings.json
     */
    private void saveAllSettings() {
        try {
            Files.writeString(Path.of(Constants.CONFIG_FILE), GsonUtil.INSTANCE.getGson().toJson(configEntity), StandardCharsets.UTF_8);
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 检查configEntity中有没有null值
     *
     * @param obj object
     * @return boolean
     */
    private boolean noNullValue(Object obj) {
        try {
            for (Field field : obj.getClass().getDeclaredFields()) {
                field.setAccessible(true);
                Object o = field.get(obj);
                if (o == null) {
                    return false;
                }
            }
        } catch (IllegalAccessException e) {
            log.error("error: {}", e.getMessage(), e);
            return false;
        }
        return true;
    }

    /**
     * 修改无效的配置，如线程数为负数等
     *
     * @param config 配置实体
     */
    private void correctInvalidConfigs(ConfigEntity config) {
        if (config.isEnableGpuAccelerate()) {
            config.setEnableGpuAccelerate(GPUAccelerator.INSTANCE.isGPUAvailableOnSystem());
        }
        int availableProcessors = Runtime.getRuntime().availableProcessors();
        int maxThreadNumber = availableProcessors * 2;
        int searchThreadNumber = config.getSearchThreadNumber();
        if (searchThreadNumber > maxThreadNumber || searchThreadNumber < 1) {
            config.setSearchThreadNumber(maxThreadNumber);
        }
        try {
            Constants.Enums.LLM.valueOf(config.getLlm());
        } catch (Exception e) {
            config.setLlm(Constants.Enums.LLM.NONE.name());
        }
    }

    /**
     * 检查配置错误
     *
     * @param event 检查配置事件
     */
    @EventRegister(registerClass = CheckConfigsEvent.class)
    private static void checkConfigsEvent(Event event) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(checkPriorityFolder(getInstance().configEntity));
        if (!stringBuilder.toString().isEmpty()) {
            stringBuilder.append("\n");
        }
        event.setReturnValue(stringBuilder.toString());
    }

    /**
     * 读取所有配置
     *
     * @param event 读取配置事件
     */
    @EventRegister(registerClass = SetConfigsEvent.class)
    private static void setAllConfigsEvent(Event event) {
        SetConfigsEvent setConfigsEvent = (SetConfigsEvent) event;
        AllConfigs allConfigs = getInstance();
        if (setConfigsEvent.getConfigs() == null) {
            // MainClass初始化
            allConfigs.readAllSettings();
            allConfigs.saveAllSettings();
        } else {
            ConfigEntity tempConfigEntity = setConfigsEvent.getConfigs();

            // 添加高级设置参数
            if (!allConfigs.noNullValue(tempConfigEntity.getAdvancedConfigEntity())) {
                Map<String, Object> configsJson = allConfigs.getSettingsJSON();
                AdvancedConfigEntity advancedConfigEntity = allConfigs.readAdvancedConfigs(configsJson);
                tempConfigEntity.setAdvancedConfigEntity(advancedConfigEntity);
            }

            // 更新设置
            if (allConfigs.noNullValue(tempConfigEntity)) {
                allConfigs.correctInvalidConfigs(tempConfigEntity);
                allConfigs.configEntity = tempConfigEntity;
                allConfigs.saveAllSettings();
            } else {
                throw new NullPointerException("configEntity中有Null值");
            }
        }
    }

    /**
     * 在系统启动后添加一个钩子，在Windows关闭时自动退出
     *
     * @param event 事件
     */
    @EventListener(listenClass = BootSystemEvent.class)
    private static void shutdownListener(Event event) {
        Runtime.getRuntime().addShutdownHook(new Thread(() -> EventManagement.getInstance().putEvent(new CloseEvent())));
    }
}
