package file.engine.utils.llm;

import file.engine.configs.AllConfigs;
import file.engine.configs.Constants;
import file.engine.utils.llm.impl.OllamaLLM;
import io.github.ollama4j.types.OllamaModelType;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

public class LLMFactory {

    public static Optional<LLMInterface> createLLM(String llmName) {
        try {
            AllConfigs allConfigs = AllConfigs.getInstance();
            Map<String, Object> llmConfigsAll = allConfigs.getConfigEntity().getLlmConfigs();
            Constants.Enums.LLM llm = Constants.Enums.LLM.valueOf(llmName);
            Map<String, Object> llmConfigs = (Map<String, Object>) llmConfigsAll.getOrDefault(llmName, new HashMap<>());
            LLMInterface llmInstance;
            switch (llm) {
                case OLLAMA -> {
                    String address = (String) llmConfigs.getOrDefault("address", "http://localhost:11434");
                    String modelType = (String) llmConfigs.getOrDefault("modelType", OllamaModelType.QWEN2);
                    String apiKey = (String) llmConfigs.getOrDefault("apiKey", "");
                    llmInstance = new OllamaLLM(address, modelType, apiKey);
                }
                default -> {
                    return Optional.empty();
                }
            }
            llmInstance.init();
            return Optional.of(llmInstance);
        } catch (Exception e) {
            return Optional.empty();
        }
    }
}
