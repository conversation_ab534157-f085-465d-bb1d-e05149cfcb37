package file.engine.controller;

import com.google.gson.*;
import file.engine.annotation.EventListener;
import file.engine.configs.AllConfigs;
import file.engine.configs.Constants;
import file.engine.dllInterface.gpu.GPUAccelerator;
import file.engine.entity.*;
import file.engine.event.handler.Event;
import file.engine.event.handler.EventManagement;
import file.engine.event.handler.impl.BootSystemEvent;
import file.engine.event.handler.impl.configs.SetConfigsEvent;
import file.engine.event.handler.impl.database.*;
import file.engine.event.handler.impl.stop.CloseEvent;
import file.engine.services.DatabaseService;
import file.engine.services.utils.PathMatchUtil;
import file.engine.services.utils.UwpUtil;
import file.engine.services.utils.reader.FileReaderUtil;
import file.engine.utils.*;
import file.engine.utils.gson.GsonUtil;
import file.engine.utils.llm.LLMFactory;
import file.engine.utils.llm.LLMInterface;
import file.engine.utils.llm.impl.OllamaLLM;
import io.github.ollama4j.models.generate.OllamaStreamHandler;
import io.github.ollama4j.types.OllamaModelType;
import io.javalin.Javalin;
import io.javalin.community.ssl.SslPlugin;
import io.javalin.http.HttpStatus;
import io.javalin.json.JavalinGson;
import io.javalin.plugin.bundled.CorsPluginConfig;
import io.javalin.util.JavalinLogger;
import lombok.NonNull;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

import java.io.File;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.nio.file.Files;
import java.nio.file.Path;
import java.security.PrivateKey;
import java.security.cert.X509Certificate;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
public class Core {

    private static final ConcurrentLinkedQueue<DatabaseService.SearchTask> searchTaskQueue = new ConcurrentLinkedQueue<>();
    private static Javalin server;
    private static LLMInterface llm;
    private static final int[] llmMaxResultNum = {200};

    @EventListener(listenClass = SetConfigsEvent.class)
    private static void setLLM(Event event) {
        if (llm != null) {
            // 获取最新配置的LLM类型
            String newConfigLLMName = AllConfigs.getInstance().getConfigEntity().getLlm();
            Constants.Enums.LLM newConfigLLM;
            try {
                newConfigLLM = Constants.Enums.LLM.valueOf(newConfigLLMName);
            } catch (Exception e) {
                newConfigLLM = Constants.Enums.LLM.NONE;
            }

            // 获取当前LLM类型
            Constants.Enums.LLM existLLM;
            if (llm instanceof OllamaLLM) {
                existLLM = Constants.Enums.LLM.OLLAMA;
            } else {
                existLLM = Constants.Enums.LLM.NONE;
            }

            // 类型相同，继续检查配置是否有更新
            if (newConfigLLM == existLLM) {
                if (existLLM == Constants.Enums.LLM.OLLAMA) {
                    var ollamaLLM = (OllamaLLM) llm;
                    String oldAddress = ollamaLLM.getAddress();
                    String oldLlmModelType = ollamaLLM.getLlmModelType();
                    String oldApiKey = ollamaLLM.getApiKey();

                    Map<String, Object> llmConfigsAll = AllConfigs.getInstance().getConfigEntity().getLlmConfigs();
                    Map<String, Object> llmConfigs = (Map<String, Object>) llmConfigsAll.getOrDefault(newConfigLLMName, new HashMap<>());
                    String address = (String) llmConfigs.getOrDefault("address", "http://localhost:11434");
                    String modelType = (String) llmConfigs.getOrDefault("modelType", OllamaModelType.QWEN2);
                    String apiKey = (String) llmConfigs.getOrDefault("apiKey", "");
                    // 配置也相同，直接退出，不需要更新LLM
                    if (Objects.equals(oldAddress, address) &&
                        Objects.equals(modelType, oldLlmModelType) &&
                        Objects.equals(oldApiKey, apiKey)) {
                        return;
                    }
                }
            }
        }

        ThreadPoolUtil.INSTANCE.executeTask(() -> {
            var llmOpt = LLMFactory.createLLM(AllConfigs.getInstance().getConfigEntity().getLlm());
            llmOpt.ifPresent(llmInterface -> {
                llm = llmInterface;
                registerLLMPlugin(llmInterface, llmMaxResultNum);
            });
        });
    }

    @EventListener(listenClass = BootSystemEvent.class)
    @SneakyThrows
    private static void startServer(Event event) {
        JavalinLogger.enabled = false;
        var databaseService = DatabaseService.getInstance();
        var eventManager = EventManagement.getInstance();
        var summarizedTokenMap = new ConcurrentHashMap<String, ArrayDeque<SummaryFileStatus>>();

        Gson gson = GsonUtil.INSTANCE.getGson();
        var app = Javalin.create(config -> {
                    config.bundledPlugins.enableCors(corsPluginConfig -> corsPluginConfig.addRule(CorsPluginConfig.CorsRule::anyHost));
                    config.jsonMapper(new JavalinGson(gson, true));
                    SslPlugin plugin = new SslPlugin(conf -> {
                        String location = "aiverything";
                        String caCerFile = "ca.cer";
                        String caKeyFile = "ca.key";
                        String appdata = System.getenv("APPDATA");
                        Path certificatePath = Path.of(appdata, location, caCerFile);
                        Path certificateKeyPath = Path.of(appdata, location, caKeyFile);

                        try (var caIs = Files.newInputStream(certificatePath);
                             var keyIs = Files.newInputStream(certificateKeyPath)) {
                            X509Certificate x509Certificate = CertificateUtil.loadCertificate(caIs);
                            PrivateKey privateKey = CertificateUtil.loadPrivateKey(keyIs);
                            GeneratedCertificate result = CertificateUtil.generateCertificate(x509Certificate, privateKey);
                            conf.keystoreFromPath(result.file().getCanonicalPath(), result.password());

                            conf.securePort = ((BootSystemEvent) event).port;
                            conf.insecure = false;
                            conf.sniHostCheck = false;
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                    });
                    config.registerPlugin(plugin);
                })
                .exception(Exception.class, (e, ctx) -> log.error("error {}, ", e.getMessage(), e))
                .error(HttpStatus.NOT_FOUND, ctx -> ctx.json("not found"))
                .afterMatched(ctx -> {
                    ctx.header("Access-Control-Allow-Origin", "*")
                            .header("Access-Control-Allow-Credentials", "true")
                            .header("Access-Control-Allow-Methods", "*")
                            .header("Access-Control-Max-Age", "7200")
                            .header("Access-Control-Allow-Headers", "Origin, X-Requested-With, Content-Type, Accept, token");

                    String result = ctx.result();
                    String contentType = ctx.res().getContentType();
                    if (contentType != null && contentType.contains("json")) {
                        JsonElement jsonElement = result == null ? null : JsonParser.parseString(result);
                        Object data = null;
                        if (jsonElement != null) {
                            switch (jsonElement) {
                                case JsonArray ignored -> data = gson.fromJson(jsonElement, List.class);
                                case JsonObject ignored -> data = gson.fromJson(jsonElement, Map.class);
                                case JsonPrimitive ignored -> data = gson.fromJson(jsonElement, Object.class);
                                default -> {
                                }
                            }
                        }
                        ApiResponse apiResponse = new ApiResponse(ctx.res().getStatus(), data);
                        ctx.json(apiResponse);
                    } else {
                        ctx.json(new ApiResponse(ctx.res().getStatus(), result));
                    }
                })
                .get("/icon", ctx -> {
                    var path = ctx.queryParam("path");
                    if (path == null) {
                        ctx.result("");
                        return;
                    }
                    String isUwp = ctx.queryParam("isUwp");
                    var icon = IconUtil.getBase64Image(path,
                            IconUtil.getBigIcon(path,
                                    Objects.equals("true", isUwp),
                                    Constants.ICON_SIZE,
                                    Constants.ICON_SIZE),
                            Constants.ICON_SIZE,
                            Constants.ICON_SIZE);
                    ctx.result(icon);
                })
                .get("/config", ctx -> {
                    ConfigEntity configEntity = AllConfigs.getInstance().getConfigEntity();
                    ArrayList<Map<String, Object>> dataTypeList = convertFromDataTypeToList(configEntity.getDataTypeSuffixMap());
                    LinkedHashMap<String, Object> configMap = gson.fromJson(gson.toJson(configEntity), LinkedHashMap.class);
                    configMap.put("dataTypeSuffixMap", dataTypeList);
                    ctx.json(configMap);
                })
                .post("/config", ctx -> {
                    var configEntityMap = ctx.bodyAsClass(LinkedHashMap.class);
                    Object dataTypeSuffixList = configEntityMap.get("dataTypeSuffixMap");
                    Map<String, List<String>> dataTypeSuffixMap = convertFromListToDataType((List<Map<String, Object>>) dataTypeSuffixList);
                    configEntityMap.put("dataTypeSuffixMap", dataTypeSuffixMap);
                    ConfigEntity configEntity = gson.fromJson(gson.toJson(configEntityMap), ConfigEntity.class);
                    eventManager.putEvent(new SetConfigsEvent(configEntity));
                })
                .get("/gpu", ctx -> ctx.json(GPUAccelerator.INSTANCE.getDevices()))
                .get("/disk", ctx -> {
                    File[] disks = File.listRoots();
                    ArrayList<String> arraylistDisks = new ArrayList<>();
                    for (File each : disks) {
                        if (AllConfigs.getInstance().isDiskAvailable(each.getAbsolutePath())) {
                            arraylistDisks.add(each.getAbsolutePath());
                        }
                    }
                    ctx.json(arraylistDisks);
                })
                .post("/close", ctx -> eventManager.putEvent(new CloseEvent()))
                .get("/status", ctx -> ctx.result(databaseService.getRawStatus().toString()))
                // db control
                .post("/flushFileChanges", ctx -> eventManager.putEvent(new FlushFileChangesEvent()))
                .post("/optimize", ctx -> eventManager.putEvent(new OptimizeDatabaseEvent()))
                // search
                .get("/frequentResult", ctx -> {
                    int num = Integer.parseInt(Objects.requireNonNull(ctx.queryParam("num")));
                    String searchText = ctx.queryParam("searchText");

                    SearchInfoEntity searchInfoEntity;
                    if (searchText != null && !searchText.isEmpty()) {
                        searchInfoEntity = generateSearchKeywordsAndSearchCase(searchText, num);
                    } else {
                        searchInfoEntity = null;
                    }

                    var freqResultMap = databaseService.getFrequentlyUsedCachesMerged(
                            num,
                            false,
                            searchInfoEntity);
                    if (freqResultMap == null || freqResultMap.isEmpty()) {
                        return;
                    }
                    ArrayList<SearchResultWrapper> searchResultWrappers = convertFrequentResultsList(freqResultMap);
                    ctx.json(searchResultWrappers);
                })
                .post("/search", ctx -> {
                    SearchInfoEntity searchInfo = generateSearchKeywordsAndSearchCase(Objects.requireNonNull(ctx.queryParam("searchText")),
                            Integer.parseInt(Objects.requireNonNull(ctx.queryParam("maxResultNum"))));
                    var ref = searchSynchronized(searchInfo);
                    ctx.json(ref);
                })
                .post("/prepareSearch", ctx -> {
                    SearchInfoEntity searchInfo = generateSearchKeywordsAndSearchCase(Objects.requireNonNull(ctx.queryParam("searchText")),
                            Integer.parseInt(Objects.requireNonNull(ctx.queryParam("maxResultNum"))));
                    PrepareSearchEvent prepareSearchEvent = new PrepareSearchEvent(searchInfo);
                    var ref = new Object() {
                        String ret;
                    };
                    eventManager.putEvent(prepareSearchEvent, successEvent -> successEvent.getReturnValue().ifPresent(o -> {
                        DatabaseService.SearchTask searchTask = (DatabaseService.SearchTask) o;
                        if (!searchTaskQueue.contains(searchTask)) {
                            searchTaskQueue.offer(searchTask);
                        }
                        ref.ret = searchTask.getUuid().toString();
                    }), errorEvent -> {
                        StringBuilder stringBuilder = new StringBuilder();
                        stringBuilder.append("failed: ");
                        errorEvent.getException().ifPresent(ex -> stringBuilder.append(ex.getMessage()));
                        ref.ret = stringBuilder.toString();
                    });
                    eventManager.waitForEvent(prepareSearchEvent);
                    ctx.json(ref.ret);
                })
                .post("/searchAsync", ctx -> {
                    String searchText = Objects.requireNonNull(ctx.queryParam("searchText"));
                    int maxResultNum = Integer.parseInt(Objects.requireNonNull(ctx.queryParam("maxResultNum")));
                    String ret = sendSearchEvent(searchText, maxResultNum);
                    ctx.json(ret);
                })
                .get("/highlight", ctx -> {
                    var fileName = ctx.queryParam("fileName");
                    var parentPath = ctx.queryParam("parentPath");
                    var uuid = ctx.queryParam("uuid");
                    if (fileName == null || parentPath == null || uuid == null) {
                        return;
                    }
                    var taskOpt = getSearchTaskByUUID(uuid);
                    taskOpt.map(task -> {
                        var searchInfo = task.getSearchInfo();
                        var pair = PathMatchUtil.highlightKeywords(fileName, parentPath, searchInfo);
                        HashMap<String, String> highlightMap = new HashMap<>();
                        highlightMap.put("parentPath", pair.left());
                        highlightMap.put("fileName", pair.right());
                        return highlightMap;
                    }).ifPresent(ctx::json);
                })
                .delete("/summarizeAI", ctx -> {
                    String sessionId = ctx.queryParam("sessionId");
                    if (sessionId == null || sessionId.isEmpty()) {
                        throw new RuntimeException("LLM session id cannot be null");
                    }
                    summarizedTokenMap.remove(sessionId);
                    var llmOpt = Optional.ofNullable(llm);
                    llmOpt.ifPresent(llmInterface -> llmInterface.removeSession(sessionId));
                })
                .sse("/summaryStream", client -> {
                    var ctx = client.ctx();
                    String sessionId = ctx.queryParam("sessionId");
                    if (sessionId == null || sessionId.isEmpty()) {
                        throw new RuntimeException("LLM session id cannot be null");
                    }
                    client.keepAlive();
                    ctx.contentType("text/event-stream;charset=UTF-8");
                    ctx.header("Cache-Control", "no-cache")
                            .header("Connection", "keep-alive");

                    boolean[] exitSendFlag = new boolean[1];

                    client.onClose(() -> exitSendFlag[0] = true);
                    ThreadPoolUtil.INSTANCE.executeTask(() -> {
                        ArrayDeque<SummaryFileStatus> tokenQueue = summarizedTokenMap.get(sessionId);
                        if (tokenQueue != null) {
                            long startTime = System.currentTimeMillis();
                            try {
                                while (!exitSendFlag[0] && System.currentTimeMillis() - startTime < 10 * 60 * 1000) {
                                    try {
                                        SummaryFileStatus tokenResult = tokenQueue.poll();
                                        if (tokenResult != null) {
                                            String tokenJson = gson.toJson(tokenResult);
                                            client.sendEvent(tokenJson);
                                        }
                                        TimeUnit.MILLISECONDS.sleep(1);
                                    } catch (InterruptedException e) {
                                        break; // 客户端断开时退出循环
                                    }
                                }
                            } catch (Exception e) {
                                log.error("error {}", e.getMessage(), e);
                                throw new RuntimeException(e);
                            }
                        }
                    });
                })
                .get("/summarizeAI", ctx -> {
                    String sessionId = ctx.queryParam("sessionId");
                    if (sessionId == null || sessionId.isEmpty()) {
                        throw new RuntimeException("LLM session id cannot be null");
                    }
                    ArrayDeque<SummaryFileStatus> tokenQueue = summarizedTokenMap.get(sessionId);
                    if (tokenQueue != null && !tokenQueue.isEmpty()) {
                        SummaryFileStatus tokenResult = tokenQueue.poll();
                        if (tokenResult != null) {
                            ctx.json(tokenResult);
                        }
                    }
                })
                .post("/summarizeAI", ctx -> {
                    String filePath = ctx.queryParam("file");
                    if (filePath == null || filePath.isEmpty()) {
                        throw new RuntimeException("file path cannot be null or empty");
                    }
                    if (llm == null) {
                        throw new RuntimeException("llm is null");
                    }
                    log.info("Reading file content");
                    String fileContent = FileReaderUtil.readFile(filePath);
                    if (fileContent == null || fileContent.isEmpty()) {
                        String fileSessionEmpty = "empty" + filePath;
                        ArrayDeque<SummaryFileStatus> emptyFileTokenStack = new ArrayDeque<>();
                        emptyFileTokenStack.offer(new SummaryFileStatus("File is empty", false));
                        emptyFileTokenStack.offer(new SummaryFileStatus("", true));
                        summarizedTokenMap.put(fileSessionEmpty, emptyFileTokenStack);
                        ctx.result(fileSessionEmpty);
                        return;
                    }

                    log.info("File content reading finished");
                    var llmOpt = Optional.ofNullable(llm);
                    String fileSessionId = llmOpt.map(llmInterface -> {
                        String sessionId = llmInterface.newSession();
                        ArrayDeque<SummaryFileStatus> tokenQueue = new ArrayDeque<>();
                        summarizedTokenMap.put(sessionId, tokenQueue);
                        ThreadPoolUtil.INSTANCE.executeTask(() -> {
                            try {
                                summarizeFile(filePath, fileContent, sessionId, llmInterface, token ->
                                        tokenQueue.offer(new SummaryFileStatus(token, false)));
                            } finally {
                                tokenQueue.offer(new SummaryFileStatus("", true));
                            }
                        });
                        return sessionId;
                    }).orElse("");
                    ctx.result(fileSessionId);
                })
                .post("/searchAI", ctx -> {
                    String maxResultNum = ctx.queryParam("maxResultNum");
                    try {
                        if (maxResultNum != null) {
                            llmMaxResultNum[0] = Integer.parseInt(maxResultNum);
                        }
                    } catch (Exception e) {
                        StringWriter stringWriter = new StringWriter();
                        e.printStackTrace(new PrintWriter(stringWriter));
                        log.error("error {}", stringWriter);
                    }
                    var llmOpt = Optional.ofNullable(llm);
                    llmOpt.ifPresent(llm -> {
                        String llmSession = llm.newSession();
                        try {
                            boolean[] done = new boolean[1];
                            String body = ctx.body();
                            String message = body;
                            String lastMessage = "";
                            while (!done[0] && !message.isEmpty()) {
                                lastMessage = message;
                                message = llm.chatWithTools(llmSession, message, done);
                            }
                            String returnValue = StringUtils.isEmpty(message) ? lastMessage : message;
                            returnValue = Objects.equals(body, returnValue) ? "" : returnValue;
                            if (returnValue.isEmpty()) {
                                ctx.result();
                            } else {
                                Map map = gson.fromJson(returnValue, Map.class);
                                ctx.json(map);
                            }
                        } finally {
                            llm.removeSession(llmSession);
                        }
                    });
                })
                .delete("/search", ctx -> {
                    StopSearchEvent stopSearchEvent = new StopSearchEvent();
                    eventManager.putEvent(stopSearchEvent);
                    eventManager.waitForEvent(stopSearchEvent);
                })
                .get("/uwpResult", ctx -> ctx.json(
                        getUwpSearchResult(ctx.queryParam("uuid")))
                )
                .post("/runUwp", ctx -> UwpUtil.openUWP(
                        ctx.queryParam("appUserModelId")
                ))
                .get("/result", ctx -> ctx.json(
                        getSearchResults(ctx.queryParam("uuid"))
                ))
                .delete("/result", ctx -> ctx.result(
                        String.valueOf(searchTaskQueue.removeIf(searchTask -> Objects.equals(searchTask.getUuid().toString(), ctx.queryParam("uuid"))))
                ))
                // cache
                .post("/cache", ctx -> eventManager.putEvent(new AddToCacheEvent(ctx.queryParam("path"))))
                .get("/cache", ctx -> ctx.json(databaseService.getCache()))
                .delete("/cache", ctx -> eventManager.putEvent(new DeleteFromCacheEvent(ctx.queryParam("path"))))
                // index
                .post("/update", ctx -> eventManager.putEvent(new UpdateDatabaseEvent(Boolean.parseBoolean(ctx.queryParam("isDropPrevious")))))
                // suffix priority
                .post("/suffixPriority", ctx -> eventManager.putEvent(new AddToSuffixPriorityMapEvent(
                        ctx.queryParam("suffix"), Integer.parseInt(Objects.requireNonNull(ctx.queryParam("priority")))
                )))
                .delete("/suffixPriority", ctx -> eventManager.putEvent(new DeleteFromSuffixPriorityMapEvent(ctx.queryParam("suffix"))))
                .get("/suffixPriority", ctx -> ctx.json(databaseService.getPriorityMap()))
                .put("/suffixPriority", ctx -> eventManager.putEvent(new UpdateSuffixPriorityEvent(
                        ctx.queryParam("oldSuffix"), ctx.queryParam("newSuffix"), Integer.parseInt(Objects.requireNonNull(ctx.queryParam("priority")))
                )))
                .delete("/clearSuffixPriority", ctx -> eventManager.putEvent(new ClearSuffixPriorityMapEvent()))
                .get("/version", ctx -> ctx.result(AllConfigs.getVersion()))
                .get("/buildVersion", ctx -> ctx.result(AllConfigs.getBuildVersion()));
        server = app;
        app.start(((BootSystemEvent) event).port);
        startClearTaskThread();
    }

    private static void summarizeFile(String filePath,
                                      String fileContent,
                                      String fileSessionId,
                                      @NonNull LLMInterface llm,
                                      OllamaStreamHandler streamHandler) {
        if (fileContent == null || fileContent.isEmpty()) {
            return;
        }
        String message = fileContent + "\n" +
                         "以上是文件路径为：" + filePath + " 的文件内容。" +
                         "你的主要目标是将上面给出的文档的内容浓缩成一个简洁的摘要，捕捉要点和主题。" +
                         "制作最终摘要：\n" +
                         "1. 阅读摘要部分：仔细查看文档的内容的所有摘要部分。确保您清楚地了解每个部分中提供的要点、关键细节和基本信息。\n" +
                         "2. 确定主要主题： 在浏览摘要部分时，确定整个文档中普遍存在的主要主题和主题。列出这些主题，因为它们将构成您最终摘要的支柱。\n" +
                         "3. 整合信息：合并来自不同摘要部分的信息，重点关注您确定的主要主题。避免冗余并确保整合的信息按逻辑流动。\n" +
                         "4. 保留基本细节： 在整合时，确保保留对理解文档至关重要的基本细节和细微差别。考虑文档的类型和准确捕捉其本质所需的详细程度。\n" +
                         "5. 检查完整性：起草最终摘要后，对其进行审查以确保它准确代表了文档的主要思想、主题和基本细节。\n" +
                         "请记住要彻底，并确保最终摘要真实反映文档的内容和目的。";
        llm.chat(fileSessionId, message, streamHandler);
    }

    @NotNull
    private static Map<String, Object> searchSynchronized(SearchInfoEntity searchInfo) {
        StartSearchEvent startSearchEvent = new StartSearchEvent(searchInfo);
        var ref = new Object() {
            Map<String, Object> retVal;
        };
        EventManagement eventManager = EventManagement.getInstance();
        eventManager.putEvent(startSearchEvent, successEvent -> successEvent.getReturnValue().ifPresent(o -> {
            var searchTask = (DatabaseService.SearchTask) o;
            final long startTime = System.currentTimeMillis();
            while (!searchTask.isSearchDone() && System.currentTimeMillis() - startTime < Constants.MAX_TASK_EXIST_TIME) {
                try {
                    TimeUnit.MILLISECONDS.sleep(50);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            }
            HashMap<String, Object> resultWrapper = new HashMap<>();
            genSearchResultMap(searchTask, resultWrapper, searchTask.getTempResults());
            ref.retVal = resultWrapper;
        }), errorEvent -> ref.retVal = Collections.emptyMap());
        eventManager.waitForEvent(startSearchEvent);
        return ref.retVal;
    }

    @NotNull
    private static ArrayList<SearchResultWrapper> convertFrequentResultsList(Map<String, ConcurrentLinkedQueue<SearchResult>> freqResultMap) {
        var dataTypeSuffixMap = AllConfigs.getInstance().getConfigEntity().getDataTypeSuffixMap();

        // 用于存储所有已经处理过的后缀
        Set<String> processedSuffixes = new HashSet<>();

        ArrayList<SearchResultWrapper> searchResultWrappers = new ArrayList<>();
        // 先处理已知数据类型的结果
        dataTypeSuffixMap.forEach((dataType, suffixList) -> {
            ArrayList<SearchResult> container = new ArrayList<>();
            suffixList.forEach(suffix -> {
                var searchResults = freqResultMap.get(suffix);
                if (searchResults != null) {
                    container.addAll(searchResults);
                    processedSuffixes.add(suffix);
                }
            });
            searchResultWrappers.add(new SearchResultWrapper(dataType, container));
        });

        // 处理未分类的结果（放入Default类型）
        ArrayList<SearchResult> defaultContainer = new ArrayList<>();
        freqResultMap.forEach((suffix, results) -> {
            if (!processedSuffixes.contains(suffix)) {
                defaultContainer.addAll(results);
            }
        });

        if (!defaultContainer.isEmpty()) {
            searchResultWrappers.add(new SearchResultWrapper("Default", defaultContainer));
        }

        return searchResultWrappers;
    }

    @SuppressWarnings("unchecked")
    private static Map<String, List<String>> convertFromListToDataType(List<Map<String, Object>> dataTypeList) {
        LinkedHashMap<String, List<String>> dataTypeMap = new LinkedHashMap<>();
        dataTypeList.forEach(eachDataType -> {
            String dataType = (String) eachDataType.get("dataType");
            List<String> suffixList = (List<String>) eachDataType.get("suffix");
            dataTypeMap.put(dataType, suffixList);
        });
        return dataTypeMap;
    }

    @NotNull
    private static ArrayList<Map<String, Object>> convertFromDataTypeToList(Map<String, List<String>> dataTypeSuffixMap) {
        ArrayList<Map<String, Object>> dataTypeList = new ArrayList<>();
        dataTypeSuffixMap.forEach((dataType, suffixList) -> {
            HashMap<String, Object> suffixObject = new HashMap<>();
            suffixObject.put("dataType", dataType);
            suffixObject.put("suffix", suffixList);
            dataTypeList.add(suffixObject);
        });
        return dataTypeList;
    }

    private static void registerLLMPlugin(@NonNull LLMInterface llm, int[] llmMaxResultNum) {
        Args args = new Args("searchText", "string", "关键字", true);
        String apiDescription = "这是一个搜索文件的方法，方法名为search\n" +
                                "根据关键字searchText搜索文件，关键字可以是文件的内容，可以是文件名，也可以是文件路径，也可以是正则表达式，也可以是文件内容。\n" +
                                "searchText被分为两个主要部分，第一部分为关键字，第二部分为后缀条件。\n" +
                                "每个关键字之间使用  ;  (英文分号)隔开，如test;file即为搜索文件名包含test以及file关键字的文件\n" +
                                "每个后缀之间也使用  ;  (英文分号)隔开，如d;full即为搜索为文件夹且全字匹配的文件夹\n" +
                                "搜索可以指定不同的后缀条件，f代表file，即只搜索文件；d代表directory，即只搜索文件夹；p代表pattern，即关键字为正则表达式；" +
                                "c代表content，即只搜索文件内容；case代表关键字需要区分大小写。" +
                                "关键字与后缀条件之间通过 | 字符分割，即通过  |  字符将关键字和后缀条件分开，而关键字与关键字之间，后缀条件与后缀条件之间使用  ;  分开。\n" +
                                "如果关键字是文件路径，则在提取出的关键字最前方加上 / 字符。" +
                                "如果关键字是可选的，即文件路径上可以没有该关键字，或者想进行多个关键字以 or 的逻辑进行查询，则在关键字前加上?（问号）代表该关键字是可选关键字。" +
                                "以下是后缀条件的例子：" +
                                "如果用户只想搜索文件，则在关键字最后添加 |f，" +
                                "如果用户只想搜索文件夹，则在关键字最后添加 |d，" +
                                "如果用户输入的是正则表达式，则在关键字最后添加 |p，" +
                                "如果用户搜索的是文件内容中的关键字，则在关键字最后添加 |c，" +
                                "如果用户指定了需要区分大小写，则在关键字最后添加 |case" +
                                "如果用户只想搜索文件并且关键字不区分大小写，则在关键字最后添加 |f;case" +
                                "如果用户输入的是正则表达式并且只想搜索文件，则在关键字最后添加 |p;f" +
                                "例如：当用户输入  我想搜索文件名中包含 test 的文件或文件夹时，你应该调用search方法，参数searchText为 test\n" +
                                "当用户输入  搜索文件路径中包含 test-dir 的文件或文件夹时，你应该调用search方法，参数searchText为 /test-dir\n" +
                                "当用户输入  搜索文件名中包含 visual 或者包含 code的文件或文件夹时，你应该调用search方法，参数searchText为 ?visual;?code\n" +
                                "当用户输入  搜索文件名中有 test 的文件，并且只要文件结果，你应该调用search方法，参数searchText为 test|f\n" +
                                "当用户输入  我想搜索文件名中 test的文件夹，你应该调用search方法，参数searchText为 test|d\n" +
                                "当用户输入  我想搜索文件名匹配正则表达式 .?\\.txt 的文件，你应该调用search方法，参数searchText为 .?\\.txt|p\n" +
                                "当用户输入  我想搜索文件内容中包含 test 的文件，你应该调用search方法，参数searchText为 test|c\n" +
                                "当用户输入  搜索文件名中有 test 的文件，并且只要文件结果，并且 test 区分大小写，不能匹配到 Test ，你应该调用search方法，参数searchText为 test|f;case\n" +
                                "注意：只有当用户指定了只需要文件结果，或者只需要文件夹结果，才在searchText最后添加 |f 或者 |d。\n" +
                                "如果用户的描述是  找到与某个领域和方向相关的文件，那么你需要对这个领域和方向的关键字进行猜测，给出该领域可能出现的关键字，尽可能多给出一点关键字，至少给出三个，然后给出searchText参数，再调用search方法。\n" +
                                "例如：当用户输入  我想找到与卷积神经网络相关的论文  ，你需要先分析卷积神经网络领域相关的关键字，比如cnn，卷积核，全连接层，等等，对应的searchText参数为 ?cnn;?卷积核;?全连接层。\n";
        llm.registerApi("search",
                "search",
                apiDescription,
                "V1.0.0",
                List.of(args),
                argMap -> {
                    String searchText = String.valueOf(argMap.getOrDefault("searchText", ""));
                    int resultNumber = llmMaxResultNum[0];
                    SearchInfoEntity searchInfo = generateSearchKeywordsAndSearchCase(searchText, resultNumber);
                    Map<String, Object> resultMap = searchSynchronized(searchInfo);
                    Gson gson = GsonUtil.INSTANCE.getGson();
                    return gson.toJson(resultMap);
                }
        );
    }

    private static @NotNull String sendSearchEvent(String searchText, int maxResultNum) {
        var eventManager = EventManagement.getInstance();
        SearchInfoEntity searchInfo = generateSearchKeywordsAndSearchCase(searchText, maxResultNum);
        StartSearchEvent startSearchEvent = new StartSearchEvent(searchInfo);
        final String[] ret = new String[1];
        eventManager.putEvent(startSearchEvent, successEvent -> successEvent.getReturnValue().ifPresent(o -> {
            DatabaseService.SearchTask searchTask = (DatabaseService.SearchTask) o;
            if (!searchTaskQueue.contains(searchTask)) {
                searchTaskQueue.offer(searchTask);
            }
            ret[0] = searchTask.getUuid().toString();
        }), error -> {
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append("failed: ");
            error.getException().ifPresent(ex -> stringBuilder.append(ex.getMessage()));
            ret[0] = stringBuilder.toString();
        });
        eventManager.waitForEvent(startSearchEvent);
        return ret[0];
    }

    @EventListener(listenClass = CloseEvent.class)
    private static void close(Event event) {
        if (server != null) {
            server.stop();
        }
    }

    private static void startClearTaskThread() {
        ThreadPoolUtil.getInstance().executeTask(() -> {
            EventManagement eventManagement = EventManagement.getInstance();
            long startCheckTime = System.currentTimeMillis();
            while (eventManagement.notMainExit()) {
                if (System.currentTimeMillis() - startCheckTime > Constants.MAX_TASK_EXIST_TIME) {
                    startCheckTime = System.currentTimeMillis();
                    ArrayList<DatabaseService.SearchTask> taskToRemove = new ArrayList<>();
                    searchTaskQueue.forEach(searchTask -> {
                        if (System.currentTimeMillis() - searchTask.getTaskCreateTimeMills() > Constants.MAX_TASK_EXIST_TIME) {
                            taskToRemove.add(searchTask);
                        }
                    });
                    searchTaskQueue.removeAll(taskToRemove);
                }
                try {
                    TimeUnit.MILLISECONDS.sleep(100);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            }
        });
    }

    public static List<UwpResult> getUwpSearchResult(String uuid) {
        return getSearchTaskByUUID(uuid).map(currentSearchTask -> {
            var uwpResults = currentSearchTask.getUwpResults();
            return uwpResults.stream()
                    .sorted(RankUtil.createSafeUwpComparator(currentSearchTask.getSearchInfo().keywords()))
                    .toList();
        }).orElse(Collections.emptyList());
    }

    /**
     * 首先将会对cache添加到结果，随后将tempResults中的结果加入
     * 返回的data字段的map，每个dataType按照settings.json中dataTypeSuffixMap中的顺序来依次插入
     * dataType对应list中的结果顺序按照settings.json中dataType所对应的后缀来进行排序，并按照最后修改时间进行排序
     *
     * @param uuid 任务uuid
     * @return result
     */
    private static HashMap<String, Object> getSearchResults(String uuid) {
        HashMap<String, Object> retWrapper = new HashMap<>();
        getSearchTaskByUUID(uuid).ifPresent(currentSearchTask -> {
            var tempResults = currentSearchTask.getTempResults();
            genSearchResultMap(currentSearchTask, retWrapper, tempResults);
        });
        return retWrapper;
    }

    private static Optional<DatabaseService.SearchTask> getSearchTaskByUUID(String uuid) {
        DatabaseService.SearchTask currentSearchTask;
        if (uuid == null) {
            currentSearchTask = searchTaskQueue.peek();
        } else {
            var tmpList = searchTaskQueue.stream()
                    .filter(searchTask -> uuid.equals(searchTask.getUuid().toString()))
                    .toList();
            if (tmpList.isEmpty()) {
                currentSearchTask = null;
            } else {
                currentSearchTask = tmpList.getFirst();
            }
        }
        return Optional.ofNullable(currentSearchTask);
    }

    private static void genSearchResultMap(DatabaseService.SearchTask searchTask,
                                           HashMap<String, Object> retWrapper,
                                           ConcurrentHashMap<String, ConcurrentLinkedQueue<SearchResult>> resultsContainer) {
        var resultContainerCopy = new HashMap<>(resultsContainer);
        retWrapper.put("uuid", searchTask.getUuid().toString());
        // 根据dataType再次聚合，如png,jpg再次合并为一类
        List<SearchResultWrapper> resultWrapper;
        if ((resultWrapper = (List<SearchResultWrapper>) retWrapper.get("data")) == null) {
            resultWrapper = new ArrayList<>();
        }
        Map<String, List<String>> dataTypeSuffixMap = AllConfigs.getInstance().getConfigEntity().getDataTypeSuffixMap();

        // 每个dataType对应一个suffixList
        for (Map.Entry<String, List<String>> entry : dataTypeSuffixMap.entrySet()) {
            String dataType = entry.getKey();
            List<String> suffixList = entry.getValue();
            ArrayList<SearchResult> eachDataTypeContainer = new ArrayList<>();

            // 遍历suffixList处理每个后缀
            for (String suffix : suffixList) {
                ConcurrentLinkedQueue<SearchResult> searchResults = resultContainerCopy.get(suffix);
                if (searchResults == null || searchResults.isEmpty()) {
                    continue;
                }

                resultContainerCopy.remove(suffix);

                searchResults.stream()
                        .sorted(RankUtil.createSafeComparator(searchTask.getSearchInfo().keywords()))
                        .forEach(eachDataTypeContainer::add);
            }

            var dataTypeResultOpt = resultWrapper.stream()
                    .filter(each -> each.dataType().equals(dataType)).findAny();
            if (dataTypeResultOpt.isPresent()) {
                var searchResultsContainer = dataTypeResultOpt.get().results();
                for (SearchResult each : eachDataTypeContainer) {
                    if (!searchResultsContainer.contains(each)) {
                        searchResultsContainer.add(each);
                    }
                }
            } else {
                resultWrapper.add(new SearchResultWrapper(dataType, eachDataTypeContainer));
            }
        }

        // 处理文件夹类型
        ConcurrentLinkedQueue<SearchResult> dirSearchResults = resultContainerCopy.get("dirPriority");
        if (dirSearchResults != null && !dirSearchResults.isEmpty()) {
            resultContainerCopy.remove("dirPriority");

            // 按照modify date降序排列
            var searchResultsSorted = dirSearchResults
                    .stream()
                    .sorted(RankUtil.createSafeComparator(searchTask.getSearchInfo().keywords()))
                    .collect(Collectors.toCollection(ArrayList::new));

            var dataTypeResultOpt = resultWrapper.stream()
                    .filter(each -> each.dataType().equals("Folder")).findAny();
            if (dataTypeResultOpt.isPresent()) {
                var folderContainer = dataTypeResultOpt.get().results();
                for (SearchResult searchResult : searchResultsSorted) {
                    if (!folderContainer.contains(searchResult)) {
                        folderContainer.add(searchResult);
                    }
                }
            } else {
                resultWrapper.add(new SearchResultWrapper("Folder", searchResultsSorted));
            }
        }

        // 处理默认结果
        ArrayList<SearchResult> remainingResults = new ArrayList<>();
        resultContainerCopy.values().forEach(remainingResults::addAll);
        var remainingResultsSorted = remainingResults.stream()
                .sorted(RankUtil.createSafeComparator(searchTask.getSearchInfo().keywords()))
                .collect(Collectors.toCollection(ArrayList::new));

        var dataTypeResultOpt = resultWrapper.stream()
                .filter(each -> each.dataType().equals("Default")).findAny();
        if (dataTypeResultOpt.isPresent()) {
            var defaultContainer = dataTypeResultOpt.get().results();
            for (SearchResult searchResult : remainingResultsSorted) {
                if (!defaultContainer.contains(searchResult)) {
                    defaultContainer.add(searchResult);
                }
            }
        } else {
            resultWrapper.add(new SearchResultWrapper("Default", remainingResultsSorted));
        }

        retWrapper.put("data", resultWrapper);
        retWrapper.put("isDone", searchTask.isSearchDone());
        retWrapper.put("taskDuration", searchTask.getTaskExecutionDuration());
    }


    /**
     * 根据用户输入设置搜索关键字
     */
    private static SearchInfoEntity generateSearchKeywordsAndSearchCase(String searchBarText, int maxResultNum) {
        String searchText;
        String[] searchCase;
        String[] keywords;
        if (!searchBarText.isEmpty()) {
            final int i = searchBarText.lastIndexOf('|');
            if (i == -1) {
                searchCase = null;
                searchText = searchBarText;
            } else {
                searchText = searchBarText.substring(0, i);
                var searchCaseStr = searchBarText.substring(i + 1);
                if (!searchCaseStr.isEmpty()) {
                    String[] tmpSearchCase = RegexUtil.semicolon.split(searchCaseStr);
                    searchCase = new String[tmpSearchCase.length];
                    for (int j = 0; j < tmpSearchCase.length; j++) {
                        searchCase[j] = tmpSearchCase[j].trim();
                    }
                } else {
                    searchCase = null;
                }
            }
            keywords = RegexUtil.semicolon.split(searchText);
        } else {
            keywords = null;
            searchCase = null;
            searchText = "";
        }
        return new SearchInfoEntity(() -> searchText, () -> searchCase, () -> keywords, maxResultNum);
    }

}
